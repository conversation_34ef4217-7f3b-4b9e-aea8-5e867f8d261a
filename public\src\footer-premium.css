/* Footer Premium Styles */
.footer-premium {
  background: linear-gradient(120deg, var(--glass-bg) 60%, rgba(99,102,241,0.05) 100%);
  border-top: 2px solid var(--border-color);
  box-shadow: 0 -8px 32px 0 var(--shadow-primary), 0 -1.5px 6px 0 var(--shadow-secondary);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
}
.footer-premium .footer-glow {
  position: absolute;
  top: -60px;
  left: 50%;
  width: 400px;
  height: 120px;
  background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
  opacity: 0.18;
  filter: blur(16px);
  transform: translateX(-50%);
  pointer-events: none;
  z-index: 0;
}
.footer-premium .footer-content {
  position: relative;
  z-index: 1;
}
.footer-premium .footer-link {
  transition: color 0.2s, text-shadow 0.2s, transform 0.2s;
  text-shadow: 0 2px 8px rgba(129,140,248,0.10);
}
.footer-premium .footer-link:hover {
  color: var(--primary-color);
  text-shadow: 0 4px 16px var(--primary-color), 0 1.5px 6px #3b82f6;
  transform: scale(1.08) translateY(-2px);
}
.footer-premium .footer-social {
  transition: transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(129,140,248,0.10);
  border-radius: 50%;
  background: rgba(24,28,43,0.7);
  padding: 0.5rem;
  margin: 0 0.25rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.footer-premium .footer-social:hover {
  background: var(--primary-color);
  color: #fff !important;
  transform: scale(1.15) rotate(-6deg);
  box-shadow: 0 4px 16px var(--primary-color);
}
.footer-premium .footer-divider {
  border-color: var(--primary-color);
  opacity: 0.18;
}
.footer-premium .footer-copyright {
  color: var(--secondary-color);
  font-size: 0.95rem;
  letter-spacing: 0.02em;
  text-align: center;
}
