import React, { useState } from 'react';

const ProjectsPage = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const projects = [
    {
      id: 1,
      title: "TechdioApp",
      description: "A sleek, cross-platform learning hub on .NET MAUI. Browse curated tech courses, book one-on-one tutor sessions, and track your progress with offline-capable data syncing.",
      technologies: [".NET MAUI", "C#", "SQLite", "XAML"],
      icon: "fas fa-graduation-cap",
      gradient: "from-purple-500/80 to-indigo-400/80",
      year: "2024",
      category: "software",
      demoLink: "#",
      githubLink: "https://github.com/abm1119/TechdioApp"
    },
    {
      id: 2,
      title: "Carpooling App",
      description: "A comprehensive carpooling solution built with React Native, featuring real-time ride matching, secure payments, and GPS tracking for seamless shared transportation.",
      technologies: ["React Native", "Firebase", "Maps API", "Payment Gateway"],
      icon: "fas fa-car",
      gradient: "from-indigo-500/80 to-blue-400/80",
      year: "2024",
      category: "software",
      demoLink: "https://vimeo.com/1102623700",
      githubLink: "https://github.com/urooj-marvi/Carpooling_App"
    },
    {
      id: 3,
      title: "Arduino Vehicle Speed Detector",
      description: "An IR-based Arduino project that calculates and displays vehicle speed using sensor break-beams and LCD output with high accuracy measurements.",
      technologies: ["Arduino Uno", "IR Sensors", "C++", "LCD Display"],
      icon: "fas fa-microchip",
      gradient: "from-green-500/80 to-blue-400/80",
      year: "2024",
      category: "hardware",
      demoLink: "#",
      githubLink: "https://github.com/abm1119/Arduino-Vehicle-Speed-Detector-"
    }
  ];

  const categories = [
    { id: 'all', label: 'All Projects', icon: 'fas fa-th-large' },
    { id: 'software', label: 'Software', icon: 'fas fa-laptop-code' },
    { id: 'hardware', label: 'Hardware', icon: 'fas fa-microchip' },
    { id: 'tools', label: 'Tools', icon: 'fas fa-tools' },
    { id: 'fun', label: 'Fun Projects', icon: 'fas fa-gamepad' }
  ];

  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  return (
    <>
      {/* Projects Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Optimized background gradients */}
        <div className="absolute inset-0 bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary"></div>

        {/* Animated background elements */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 md:w-96 md:h-96 bg-gradient-to-br from-primary-color/20 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
          <div className="absolute bottom-1/4 right-1/4 w-48 h-48 md:w-80 md:h-80 bg-gradient-to-tr from-secondary-color/15 to-transparent rounded-full blur-3xl animate-pulse-slow2"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 md:w-48 md:h-48 bg-gradient-to-br from-accent-color/10 to-transparent rounded-full blur-2xl animate-float"></div>
        </div>

        <div className="container relative z-10 text-center py-20">
          <div className="max-w-4xl mx-auto space-y-8">
            <div className="space-y-6" data-animate>
              <h1 className="title-responsive font-bold tracking-tight leading-tight">
                <span className="block bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 bg-clip-text text-transparent font-extrabold drop-shadow-lg">
                  My Projects
                </span>
              </h1>
              <div className="mx-auto w-24 h-1 bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 rounded-full mb-6"></div>
              <p className="text-lg md:text-xl text-slate-300 font-light leading-relaxed">
                A comprehensive showcase of my technical expertise across diverse domains.
                Each project represents innovation, problem-solving, and commitment to excellence.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Section */}
      <section className="py-16 md:py-24 bg-gradient-to-b from-bg-primary via-bg-secondary/60 to-bg-primary">
        <div className="container mx-auto">
          {/* Category Navigation */}
          <div className="flex flex-wrap justify-center gap-4 mb-16" data-animate>
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveFilter(category.id)}
                className={`category-filter ${activeFilter === category.id ? 'active' : ''}`}
              >
                <i className={`${category.icon} mr-2`}></i>
                {category.label}
              </button>
            ))}
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12 max-w-7xl mx-auto" id="projects-grid">
            {filteredProjects.map((project, index) => (
              <div key={project.id} className="group card relative overflow-hidden backdrop-blur-sm project-item" data-aos="fade-up" data-aos-delay={index * 200}>
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-purple-400/20 via-indigo-400/10 to-blue-400/0 rounded-full blur-2xl opacity-30 z-0"></div>

                <div className={`project-thumb bg-gradient-to-br ${project.gradient} flex items-center justify-center h-48 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg`}>
                  <i className={`${project.icon} fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110`}></i>
                </div>

                <div className="p-8 relative z-10">
                  <div className="flex items-center gap-3 mb-3">
                    <span className="text-xs font-medium text-purple-300/80">{project.year}</span>
                    <span className="w-1 h-1 bg-purple-300/50 rounded-full"></span>
                    <span className="text-xs font-medium text-purple-300/80">{project.category.toUpperCase()}</span>
                  </div>
                  <h3 className="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">{project.title}</h3>
                  <p className="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">{project.description}</p>

                  <div className="flex flex-wrap gap-2 mb-6">
                    {project.technologies.map((tech, techIndex) => (
                      <span key={techIndex} className="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">{tech}</span>
                    ))}
                  </div>

                  <div className="flex items-center gap-3">
                    <a href={project.demoLink} target="_blank" rel="noopener noreferrer" className="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                      <span className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-purple-500/30 group-hover/btn:to-indigo-500/30"></span>
                      <span className="relative flex items-center justify-center">
                        <i className="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                        View Project
                      </span>
                    </a>
                    <a href={project.githubLink} target="_blank" rel="noopener noreferrer" className="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                      <i className="fab fa-github fa-lg"></i>
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default ProjectsPage;
