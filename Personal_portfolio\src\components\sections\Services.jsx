import React, { useState } from 'react';

const Services = () => {
  const [hoveredService, setHoveredService] = useState(null);

  const services = [
    {
      id: 1,
      icon: "fas fa-code",
      title: "Full-Stack Development",
      subtitle: "End-to-End Solutions",
      description: "Complete web and mobile applications built with cutting-edge technologies. From concept to deployment, I deliver scalable, maintainable, and high-performance solutions.",
      technologies: ["React", "Node.js", ".NET", "Python", "MongoDB", "PostgreSQL"],
      features: [
        "Responsive Web Applications",
        "Cross-Platform Mobile Apps",
        "RESTful API Development",
        "Database Design & Optimization",
        "Cloud Integration & Deployment"
      ],
      gradient: "from-blue-500/20 via-indigo-500/20 to-purple-500/20",
      iconColor: "text-blue-400",
      price: "Starting at $2,500"
    },
    {
      id: 2,
      icon: "fas fa-brain",
      title: "AI/ML Solutions",
      subtitle: "Intelligent Systems",
      description: "Custom artificial intelligence and machine learning solutions that transform your business processes and unlock valuable insights from your data.",
      technologies: ["Python", "TensorFlow", "PyTorch", "Scikit-learn", "OpenAI API", "Hugging Face"],
      features: [
        "Predictive Analytics",
        "Natural Language Processing",
        "Computer Vision Solutions",
        "Recommendation Systems",
        "Automated Decision Making"
      ],
      gradient: "from-green-500/20 via-emerald-500/20 to-teal-500/20",
      iconColor: "text-green-400",
      price: "Starting at $3,500"
    },
    {
      id: 3,
      icon: "fas fa-palette",
      title: "UI/UX Design",
      subtitle: "Digital Experiences",
      description: "User-centered design that combines aesthetics with functionality. Creating intuitive interfaces that engage users and drive conversions.",
      technologies: ["Figma", "Adobe XD", "Sketch", "Principle", "InVision", "Framer"],
      features: [
        "User Research & Analysis",
        "Wireframing & Prototyping",
        "Visual Design Systems",
        "Usability Testing",
        "Brand Identity Design"
      ],
      gradient: "from-pink-500/20 via-rose-500/20 to-red-500/20",
      iconColor: "text-pink-400",
      price: "Starting at $1,800"
    },
    {
      id: 4,
      icon: "fas fa-graduation-cap",
      title: "Technical Training",
      subtitle: "Knowledge Transfer",
      description: "Comprehensive training programs and workshops designed to upskill your team with the latest technologies and best practices in software development.",
      technologies: ["Custom Curriculum", "Hands-on Labs", "Real Projects", "Mentorship", "Certification"],
      features: [
        "Corporate Training Programs",
        "One-on-One Mentoring",
        "Workshop Development",
        "Technical Documentation",
        "Skill Assessment & Certification"
      ],
      gradient: "from-yellow-500/20 via-orange-500/20 to-amber-500/20",
      iconColor: "text-yellow-400",
      price: "Starting at $150/hour"
    },
    {
      id: 5,
      icon: "fas fa-rocket",
      title: "Startup Consulting",
      subtitle: "Business Growth",
      description: "Strategic technology consulting for startups and growing businesses. From MVP development to scaling strategies, I help turn your vision into reality.",
      technologies: ["MVP Development", "Tech Stack Selection", "Architecture Design", "Team Building"],
      features: [
        "Technology Strategy Planning",
        "MVP Development & Launch",
        "Scalability Architecture",
        "Team Building & Hiring",
        "Investor Pitch Support"
      ],
      gradient: "from-purple-500/20 via-violet-500/20 to-indigo-500/20",
      iconColor: "text-purple-400",
      price: "Custom Pricing"
    },
    {
      id: 6,
      icon: "fas fa-shield-alt",
      title: "DevOps & Security",
      subtitle: "Infrastructure Excellence",
      description: "Robust DevOps practices and security implementations that ensure your applications are reliable, scalable, and secure in production environments.",
      technologies: ["Docker", "Kubernetes", "AWS", "Azure", "Jenkins", "Terraform"],
      features: [
        "CI/CD Pipeline Setup",
        "Cloud Infrastructure Management",
        "Security Audits & Implementation",
        "Performance Monitoring",
        "Disaster Recovery Planning"
      ],
      gradient: "from-cyan-500/20 via-blue-500/20 to-indigo-500/20",
      iconColor: "text-cyan-400",
      price: "Starting at $2,000"
    }
  ];

  return (
    <section id="services" className="py-24 bg-gradient-to-b from-bg-primary via-bg-secondary/60 to-bg-primary relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-primary-color/10 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-tr from-secondary-color/8 to-transparent rounded-full blur-3xl animate-pulse-slow2"></div>
      </div>

      <div className="container mx-auto relative z-10">
        <div className="text-center mb-20">
          <h2 className="section-heading mb-6" data-animate>Professional Services</h2>
          <div className="w-32 h-1 bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 mx-auto rounded-full mb-8"></div>
          <p className="text-slate-300 text-xl max-w-3xl mx-auto leading-relaxed font-light">
            Transforming ideas into digital excellence with cutting-edge solutions and innovative approaches.
            <span className="block mt-2 text-indigo-400 font-medium">Let's build something extraordinary together.</span>
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {services.map((service, index) => (
            <div
              key={service.id}
              className="group relative"
              onMouseEnter={() => setHoveredService(service.id)}
              onMouseLeave={() => setHoveredService(null)}
              data-animate
            >
              {/* Service Card */}
              <div className={`relative h-full bg-gradient-to-br from-slate-800/90 via-slate-800/80 to-slate-900/90 rounded-3xl p-8 shadow-2xl border border-indigo-500/10 backdrop-blur-md overflow-hidden transition-all duration-500 hover:-translate-y-3 hover:shadow-3xl ${hoveredService === service.id ? 'scale-105' : ''}`}>

                {/* Animated Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${service.gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl`}></div>

                {/* Floating Elements */}
                <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-indigo-400/20 via-blue-400/10 to-purple-400/5 rounded-full blur-2xl opacity-30 group-hover:opacity-60 transition-opacity duration-500"></div>

                <div className="relative z-10">
                  {/* Service Icon */}
                  <div className="flex items-center justify-between mb-6">
                    <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${service.gradient} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      <i className={`${service.icon} text-2xl ${service.iconColor} group-hover:text-white transition-colors duration-300`}></i>
                    </div>
                    <div className="text-right">
                      <span className="text-sm text-slate-400 font-medium">{service.subtitle}</span>
                      <div className="text-lg font-bold text-indigo-400 group-hover:text-white transition-colors duration-300">{service.price}</div>
                    </div>
                  </div>

                  {/* Service Title */}
                  <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-white transition-colors duration-300">
                    {service.title}
                  </h3>

                  {/* Service Description */}
                  <p className="text-slate-300 mb-6 leading-relaxed group-hover:text-slate-200 transition-colors duration-300">
                    {service.description}
                  </p>

                  {/* Technologies */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-indigo-400 mb-3 uppercase tracking-wider">Technologies</h4>
                    <div className="flex flex-wrap gap-2">
                      {service.technologies.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-3 py-1 text-xs font-medium bg-slate-700/50 text-slate-300 rounded-full border border-slate-600/30 group-hover:bg-indigo-500/20 group-hover:border-indigo-400/30 group-hover:text-indigo-300 transition-all duration-300"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Features */}
                  <div className="mb-8">
                    <h4 className="text-sm font-semibold text-indigo-400 mb-3 uppercase tracking-wider">Key Features</h4>
                    <ul className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-sm text-slate-300 group-hover:text-slate-200 transition-colors duration-300">
                          <i className="fas fa-check-circle text-green-400 mr-3 text-xs"></i>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* CTA Button */}
                  <div className="flex gap-3">
                    <a
                      href="#contact"
                      className="flex-1 bg-gradient-to-r from-indigo-600 to-blue-600 text-white py-3 px-6 rounded-xl font-semibold text-center hover:from-indigo-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
                    >
                      Get Started
                    </a>
                    <button className="px-4 py-3 rounded-xl border border-indigo-500/30 text-indigo-400 hover:bg-indigo-500/10 hover:border-indigo-400/50 transition-all duration-300">
                      <i className="fas fa-info-circle"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA Section */}
        <div className="text-center mt-20" data-animate>
          <div className="bg-gradient-to-r from-slate-800/60 via-slate-800/40 to-slate-800/60 rounded-3xl p-12 border border-indigo-500/20 backdrop-blur-lg">
            <h3 className="text-3xl font-bold text-white mb-4">Ready to Start Your Project?</h3>
            <p className="text-slate-300 text-lg mb-8 max-w-2xl mx-auto">
              Let's discuss your requirements and create a custom solution that perfectly fits your needs and budget.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#contact"
                className="btn btn-primary btn-lg group"
              >
                <i className="fas fa-rocket mr-2"></i>
                Start Your Project
                <i className="fas fa-arrow-right group-hover:translate-x-1 transition-transform ml-2"></i>
              </a>
              <a
                href="https://calendly.com/abdulbasitmemon"
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-secondary btn-lg group"
              >
                <i className="fas fa-calendar-alt mr-2"></i>
                Schedule a Call
                <i className="fas fa-external-link-alt group-hover:scale-110 transition-transform ml-2"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
