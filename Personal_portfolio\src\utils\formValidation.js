export const validateEmail = (email) => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
};

export const validateForm = (formData) => {
  const errors = {};

  // Validate full name
  if (!formData.fullName || formData.fullName.trim().length < 2) {
    errors.fullName = 'Full name is required and must be at least 2 characters';
  }

  // Validate email
  if (!formData.email) {
    errors.email = 'Email is required';
  } else if (!validateEmail(formData.email)) {
    errors.email = 'Please enter a valid email address';
  }

  // Validate subject
  if (!formData.subject || formData.subject.trim().length < 3) {
    errors.subject = 'Subject is required and must be at least 3 characters';
  }

  // Validate message
  if (!formData.message || formData.message.trim().length < 10) {
    errors.message = 'Message is required and must be at least 10 characters';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

export const submitContactForm = async (formData) => {
  try {
    const response = await fetch('https://api.web3forms.com/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        access_key: 'd4e644bf-1892-44d2-94d8-9947f958c9aa',
        name: formData.fullName,
        email: formData.email,
        subject: formData.subject,
        message: formData.message,
        from_name: 'Abdul Basit Portfolio Contact',
      })
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      return { success: true, message: 'Message sent successfully!' };
    } else {
      throw new Error(result.message || 'Failed to send message');
    }
  } catch (error) {
    return { 
      success: false, 
      message: error.message || 'There was a problem sending your message. Please try again.' 
    };
  }
};
