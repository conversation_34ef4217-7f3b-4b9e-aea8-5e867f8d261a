import React from 'react';

const About = () => {
  const aboutCards = [
    {
      icon: "fas fa-plus",
      title: "Always learning",
      description: "Exploring new tech, ideas, and skills every day.",
      color: "blue"
    },
    {
      icon: "fas fa-users",
      title: "Skilled in",
      description: "C++, Python, Java, C#, .NET, React, and more",
      color: "green"
    },
    {
      icon: "fas fa-brain",
      title: "Tech Interests",
      description: "AI/ML, embedded systems, and clean UX",
      color: "purple"
    },
    {
      icon: "fas fa-star",
      title: "Rooted in Faith",
      description: "Guided by my Muslim values and principles",
      color: "yellow"
    },
    {
      icon: "fas fa-heart",
      title: "Family Business",
      description: "Entrepreneurial roots and teamwork",
      color: "pink"
    },
    {
      icon: "fas fa-graduation-cap",
      title: "Full-time Learner & Teacher",
      description: "Sharing knowledge, growing every day",
      color: "green"
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: "border-blue-500/20",
      green: "border-green-500/20",
      purple: "border-purple-500/20",
      yellow: "border-yellow-500/20",
      pink: "border-pink-500/20"
    };
    return colors[color] || "border-indigo-500/20";
  };

  const getIconColor = (color) => {
    const colors = {
      blue: "text-blue-400",
      green: "text-green-400",
      purple: "text-purple-400",
      yellow: "text-yellow-400",
      pink: "text-pink-400"
    };
    return colors[color] || "text-indigo-400";
  };

  return (
    <section id="about" className="py-16 md:py-24">
      <div className="container mx-auto">
        <h2 className="section-heading text-center mb-12 md:mb-16" data-animate>About Me</h2>
        <div className="flex flex-col md:flex-row items-center justify-center gap-16">
          <div className="md:w-1/3 flex justify-center">
            <div className="relative group flex items-center justify-center">
              <div className="absolute w-80 h-80 bg-gradient-to-tr from-indigo-500 via-blue-400 to-purple-500 rounded-full blur-2xl opacity-40 animate-pulse-slow z-0"></div>
              <img 
                src="/images/abm.png" 
                alt="Abdul Basit Memon - Full Stack Developer and AI Engineer"
                className="rounded-full w-72 h-72 object-cover object-center shadow-2xl border-8 border-white group-hover:scale-105 transition-transform duration-500 z-10"
                loading="lazy"
                width="288"
                height="288"
              />
              <div className="absolute inset-0 rounded-full border-4 border-primary-color/40 pointer-events-none animate-pulse"></div>
            </div>
          </div>
          
          <div className="md:w-2/3 flex flex-col gap-6">
            <div className="relative bg-gradient-to-br from-slate-800 via-slate-900 to-slate-800 rounded-3xl shadow-2xl p-10 border-0 overflow-hidden group transition-all duration-500 hover:scale-[1.025]">
              <div className="absolute -top-10 -left-10 w-40 h-40 bg-gradient-to-tr from-indigo-500 via-blue-400 to-purple-500 rounded-full blur-2xl opacity-30 animate-pulse-slow"></div>
              <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-gradient-to-tr from-purple-500 via-indigo-400 to-blue-500 rounded-full blur-2xl opacity-20 animate-pulse-slow2"></div>
              
              <h3 className="font-poppins text-3xl md:text-4xl font-extrabold text-white mb-4 flex items-center gap-3 drop-shadow-lg">
                <svg className="w-9 h-9 text-indigo-400" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
                Who I Am
              </h3>
              
              <p className="text-xl md:text-2xl text-slate-200 font-light leading-relaxed mb-2 tracking-wide">
                <span className="font-bold text-primary-color">Abdul Basit Memon</span> — tech enthusiast, indie hacker, and founder.<br/>
                <span className="text-slate-300">I build software that solves real-world problems and helps people grow. With a solid background in engineering, leadership, and business, I work at the intersection of <span className="font-semibold text-primary-color">code</span> and <span className="font-semibold text-primary-color">impact</span>.</span>
              </p>
              
              <div className="flex flex-wrap gap-3 mt-4">
                <span className="inline-flex items-center gap-2 px-4 py-1 rounded-full bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white font-semibold shadow-lg text-xs md:text-sm tracking-widest uppercase">Founder</span>
                <span className="inline-flex items-center gap-2 px-4 py-1 rounded-full bg-slate-800 text-primary-color font-semibold shadow text-xs md:text-sm tracking-widest uppercase border border-primary-color/30">Engineer</span>
                <span className="inline-flex items-center gap-2 px-4 py-1 rounded-full bg-slate-800 text-purple-400 font-semibold shadow text-xs md:text-sm tracking-widest uppercase border border-purple-400/30">Leader</span>
                <span className="inline-flex items-center gap-2 px-4 py-1 rounded-full bg-slate-800 text-blue-400 font-semibold shadow text-xs md:text-sm tracking-widest uppercase border border-blue-400/30">Indie Hacker</span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-2">
              {aboutCards.map((card, index) => (
                <div key={index} className={`flex items-start gap-4 bg-slate-800/80 rounded-xl p-5 shadow border ${getColorClasses(card.color)}`}>
                  <i className={`${card.icon} text-2xl ${getIconColor(card.color)} mt-1`}></i>
                  <div>
                    <span className="font-semibold text-white">{card.title}</span>
                    <p className="text-slate-400 text-sm mt-1">{card.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
