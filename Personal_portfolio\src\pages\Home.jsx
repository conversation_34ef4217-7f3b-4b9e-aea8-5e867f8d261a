import React, { useEffect } from 'react';
import Hero from '../components/sections/Hero';
import About from '../components/sections/About';
import Skills from '../components/sections/Skills';
import Projects from '../components/sections/Projects';
import Services from '../components/sections/Services';
import Prologware from '../components/sections/Prologware';
import Contact from '../components/sections/Contact';
import { useSmoothScroll } from '../hooks/useSmoothScroll';
import { useIntersectionObserver } from '../hooks/useIntersectionObserver';

const Home = () => {
  useSmoothScroll();
  useIntersectionObserver();

  useEffect(() => {
    // Enhanced smooth scrolling and active navigation
    const sections = document.querySelectorAll('section[id]');
    
    // Smooth scrolling for navigation links
    const handleSmoothScroll = (e) => {
      if (e.target.getAttribute('href')?.startsWith('#')) {
        e.preventDefault();
        const target = document.querySelector(e.target.getAttribute('href'));
        if (target) {
          const navbarHeight = 80;
          const targetPosition = target.offsetTop - navbarHeight - 20;

          window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
          });
        }
      }
    };

    document.addEventListener('click', handleSmoothScroll);

    // Active navigation highlighting
    const updateActiveNavigation = () => {
      const scrollPosition = window.scrollY + 100;

      sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');

        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
          // Remove active class from all nav items
          document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));

          // Add active class to current section's nav items
          const activeLinks = document.querySelectorAll(`a[href="#${sectionId}"]`);
          activeLinks.forEach(link => {
            if (link.classList.contains('nav-link')) {
              link.classList.add('active');
            }
          });
        }
      });
    };

    // Throttled scroll listener for active navigation
    let navigationTicking = false;
    const handleScroll = () => {
      if (!navigationTicking) {
        requestAnimationFrame(() => {
          updateActiveNavigation();
          navigationTicking = false;
        });
        navigationTicking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    // Intersection Observer for animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '1';
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.card, .glass-card').forEach(el => {
      el.style.opacity = '0';
      el.style.transform = 'translateY(20px)';
      el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
      observer.observe(el);
    });

    // Cleanup
    return () => {
      document.removeEventListener('click', handleSmoothScroll);
      window.removeEventListener('scroll', handleScroll);
      observer.disconnect();
    };
  }, []);

  return (
    <>
      <Hero />
      <About />
      <Skills />
      <Projects />
      <Services />
      <Prologware />
      <Contact />
    </>
  );
};

export default Home;
