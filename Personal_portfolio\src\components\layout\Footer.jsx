import React from 'react';

const Footer = () => {
  return (
    <footer className="footer-premium mt-16 relative">
      <div className="footer-glow"></div>
      <div className="footer-content container mx-auto px-5 py-10 grid grid-cols-1 md:grid-cols-4 gap-10">
        <div>
          <a href="https://linktr.ee/abdulbasitmemon" target="_blank" rel="noopener noreferrer" className="flex flex-col items-center mb-4">
            <img 
              src="/images/logo-removebg-preview.png"
              className="h-24 transition-transform transform hover:scale-110"
              alt="<PERSON> Logo - Professional Portfolio"
              loading="lazy"
              width="96"
              height="96"
            />
            <span className="mt-2 text-white font-semibold text-lg"><PERSON></span>
          </a>
        </div>
        <div>
          <h2 className="mb-4 text-lg font-bold text-secondary-color uppercase tracking-widest">Visit Here</h2>
          <ul className="space-y-2">
            <li><a href="#services" className="footer-link">Services</a></li>
            <li><a href="/projects" className="footer-link">Projects</a></li>
            <li><a href="https://abdulbasitmemon.notion.site/d9cdb927bf1f48b6ad6abc54cf5b86a1?pvs=74" target="_blank" rel="noopener noreferrer" className="footer-link">My Notion</a></li>
          </ul>
        </div>
        <div>
          <h2 className="mb-4 text-lg font-bold text-secondary-color uppercase tracking-widest">Prologware Solutions</h2>
          <ul className="space-y-2">
            <li><a href="https://resources-heaven.super.site/" target="_blank" rel="noopener noreferrer" className="footer-link">Resources Heaven</a></li>
            <li><a href="https://chat.whatsapp.com/H4mF4unGvDI3vgCFMDl3Vs" target="_blank" rel="noopener noreferrer" className="footer-link">Internships & Jobs</a></li>
            <li><a href="https://xtiles.app/65a2a3cc83f7541b54f335f8" target="_blank" rel="noopener noreferrer" className="footer-link">Community</a></li>
          </ul>
        </div>
        <div>
          <h2 className="mb-4 text-lg font-bold text-secondary-color uppercase tracking-widest">Additional Info</h2>
          <ul className="space-y-2">
            <li><a href="/community-guidelines" className="footer-link">Community Guideline</a></li>
            <li><a href="https://docs.google.com/forms/d/e/1FAIpQLSdKa4MB9goqYJYTbNhQCNE1GCjIBV43OazjQ2KU2CTXUHrNWQ/viewform" target="_blank" rel="noopener noreferrer" className="footer-link">Suggest & Request</a></li>
            <li><a href="#" className="footer-link">Events</a></li>
          </ul>
        </div>
      </div>
      <hr className="footer-divider my-6 mx-auto w-11/12" />
      <div className="footer-content container mx-auto px-5 py-4 flex flex-col md:flex-row justify-between items-center">
        <span className="footer-copyright">© 2024 <a href="https://linktr.ee/abdulbasitmemon" target="_blank" rel="noopener noreferrer" className="footer-link">Abdul Basit MEMON™</a>. All Rights Reserved.</span>
        <div className="flex mt-4 md:mt-0 space-x-4">
          <a href="https://www.linkedin.com/in/abdul-basit-memon-614961166/" target="_blank" rel="noopener noreferrer" className="footer-social" aria-label="LinkedIn">
            <i className="fab fa-linkedin-in fa-lg"></i>
          </a>
          <a href="https://x.com/AbdAbdulbasit1" target="_blank" rel="noopener noreferrer" className="footer-social" aria-label="Twitter">
            <i className="fab fa-twitter fa-lg"></i>
          </a>
          <a href="https://www.instagram.com/abdulbasit_memon/" target="_blank" rel="noopener noreferrer" className="footer-social" aria-label="Instagram">
            <i className="fab fa-instagram fa-lg"></i>
          </a>
          <a href="https://github.com/abm1119" target="_blank" rel="noopener noreferrer" className="footer-social" aria-label="GitHub">
            <i className="fab fa-github fa-lg"></i>
          </a>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
