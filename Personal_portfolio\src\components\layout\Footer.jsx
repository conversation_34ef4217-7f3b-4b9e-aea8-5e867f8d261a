import React from 'react';
import { Link } from 'react-router-dom';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: "Quick Links",
      links: [
        { label: "About", href: "#about", internal: true },
        { label: "Services", href: "#services", internal: true },
        { label: "Projects", href: "/projects", internal: true },
        { label: "Contact", href: "#contact", internal: true }
      ]
    },
    {
      title: "Prologware Solutions",
      links: [
        { label: "Resources Heaven", href: "https://resources-heaven.super.site/", external: true },
        { label: "Community", href: "https://nas.io/prologware-solutions-3", external: true },
        { label: "Internships & Jobs", href: "https://chat.whatsapp.com/H4mF4unGvDI3vgCFMDl3Vs", external: true },
        { label: "Tech Blog", href: "https://abdulbasitmemon.hashnode.dev/", external: true }
      ]
    },
    {
      title: "Resources",
      links: [
        { label: "My Notion", href: "https://abdulbasitmemon.notion.site/d9cdb927bf1f48b6ad6abc54cf5b86a1?pvs=74", external: true },
        { label: "GitHub", href: "https://github.com/abm1119", external: true },
        { label: "LinkedIn", href: "https://www.linkedin.com/in/abdul-basit-memon-614961166/", external: true },
        { label: "Resume", href: "/images/Resume.pdf", external: true }
      ]
    }
  ];

  const socialLinks = [
    { icon: "fab fa-linkedin-in", href: "https://www.linkedin.com/in/abdul-basit-memon-614961166/", label: "LinkedIn", color: "hover:text-blue-400" },
    { icon: "fab fa-github", href: "https://github.com/abm1119", label: "GitHub", color: "hover:text-gray-300" },
    { icon: "fab fa-twitter", href: "https://x.com/AbdAbdulbasit1", label: "Twitter", color: "hover:text-blue-400" },
    { icon: "fab fa-instagram", href: "https://www.instagram.com/abdulbasit_memon/", label: "Instagram", color: "hover:text-pink-400" },
    { icon: "fab fa-youtube", href: "https://youtube.com/@abdulbasitmemon", label: "YouTube", color: "hover:text-red-400" },
    { icon: "fab fa-hashnode", href: "https://abdulbasitmemon.hashnode.dev/", label: "Blog", color: "hover:text-blue-400" }
  ];

  return (
    <footer className="relative bg-gradient-to-b from-bg-secondary via-bg-primary to-slate-900 border-t border-indigo-500/20 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-32 bg-gradient-to-r from-indigo-500/10 via-blue-500/5 to-purple-500/10 blur-3xl"></div>
        <div className="absolute bottom-0 left-1/4 w-64 h-64 bg-gradient-to-br from-indigo-500/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-48 h-48 bg-gradient-to-br from-purple-500/5 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Brand Section */}
            <div className="lg:col-span-1">
              <div className="flex flex-col items-center lg:items-start">
                <Link to="/" className="group mb-6">
                  <div className="relative">
                    <img
                      src="/images/logo-removebg-preview.png"
                      className="h-20 w-20 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3"
                      alt="Abdul Basit Memon Logo"
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                </Link>
                <h3 className="text-2xl font-bold text-white mb-3 text-center lg:text-left">Abdul Basit Memon</h3>
                <p className="text-slate-400 text-center lg:text-left mb-6 leading-relaxed">
                  Full-Stack Developer & AI Engineer passionate about creating innovative solutions that make a difference.
                </p>

                {/* Quick Stats */}
                <div className="grid grid-cols-2 gap-4 w-full max-w-xs">
                  <div className="text-center lg:text-left">
                    <div className="text-2xl font-bold text-indigo-400">5+</div>
                    <div className="text-xs text-slate-500 uppercase tracking-wider">Years Exp</div>
                  </div>
                  <div className="text-center lg:text-left">
                    <div className="text-2xl font-bold text-blue-400">50+</div>
                    <div className="text-xs text-slate-500 uppercase tracking-wider">Projects</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer Links */}
            <div className="lg:col-span-3">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {footerSections.map((section, index) => (
                  <div key={index}>
                    <h4 className="text-lg font-bold text-white mb-6 relative">
                      {section.title}
                      <div className="absolute -bottom-2 left-0 w-12 h-0.5 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
                    </h4>
                    <ul className="space-y-3">
                      {section.links.map((link, linkIndex) => (
                        <li key={linkIndex}>
                          {link.internal ? (
                            link.href.startsWith('#') ? (
                              <a
                                href={link.href}
                                className="text-slate-400 hover:text-white transition-colors duration-300 flex items-center gap-2 group"
                              >
                                <i className="fas fa-chevron-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                                {link.label}
                              </a>
                            ) : (
                              <Link
                                to={link.href}
                                className="text-slate-400 hover:text-white transition-colors duration-300 flex items-center gap-2 group"
                              >
                                <i className="fas fa-chevron-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                                {link.label}
                              </Link>
                            )
                          ) : (
                            <a
                              href={link.href}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-slate-400 hover:text-white transition-colors duration-300 flex items-center gap-2 group"
                            >
                              <i className="fas fa-external-link-alt text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                              {link.label}
                            </a>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter Section */}
        <div className="py-12 border-t border-slate-700/50">
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="text-2xl font-bold text-white mb-4">Stay Updated</h3>
            <p className="text-slate-400 mb-8 max-w-2xl mx-auto">
              Get notified about new projects, blog posts, and exciting opportunities in the tech world.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-6 py-3 bg-slate-800/50 border border-slate-600/50 rounded-xl text-white placeholder:text-slate-400 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
              />
              <button className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-xl font-semibold hover:from-indigo-700 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Social Links & Copyright */}
        <div className="py-8 border-t border-slate-700/50">
          <div className="flex flex-col md:flex-row justify-between items-center gap-6">
            {/* Social Links */}
            <div className="flex items-center gap-4">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={`w-12 h-12 bg-slate-800/50 border border-slate-600/30 rounded-xl flex items-center justify-center text-slate-400 transition-all duration-300 hover:border-slate-500/50 hover:bg-slate-700/50 hover:scale-110 ${social.color}`}
                  aria-label={social.label}
                >
                  <i className={`${social.icon} text-lg`}></i>
                </a>
              ))}
            </div>

            {/* Copyright */}
            <div className="text-center md:text-right">
              <p className="text-slate-400 text-sm">
                © {currentYear} <span className="text-white font-semibold">Abdul Basit Memon</span>. All rights reserved.
              </p>
              <p className="text-slate-500 text-xs mt-1">
                Built with ❤️ using React & Tailwind CSS
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 opacity-50"></div>
    </footer>
  );
};

export default Footer;
