import React, { useState } from 'react';

const Hero = () => {
  const [isWaving, setIsWaving] = useState(false);

  const handleWaveClick = () => {
    setIsWaving(true);
    setTimeout(() => setIsWaving(false), 1200);
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Optimized background gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary"></div>

      {/* Animated background elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 md:w-96 md:h-96 bg-gradient-to-br from-primary-color/20 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-48 h-48 md:w-80 md:h-80 bg-gradient-to-tr from-secondary-color/15 to-transparent rounded-full blur-3xl animate-pulse-slow2"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 md:w-48 md:h-48 bg-gradient-to-br from-accent-color/10 to-transparent rounded-full blur-2xl animate-float"></div>
      </div>

      <div className="container relative z-10 text-center py-20">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Premium Hero Typography */}
          <div className="space-y-6" data-animate>
            <h1 className="title-responsive font-bold tracking-tight leading-tight">
              <span 
                id="waveHand" 
                className={`inline-block text-4xl md:text-6xl cursor-pointer transition-transform duration-300 hover:scale-110 touch-target ${isWaving ? 'animate-wave' : ''}`}
                onClick={handleWaveClick}
              >
                👋
              </span>
              <span className="block mt-3 bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 bg-clip-text text-transparent font-extrabold drop-shadow-lg">
                Abdul Basit Memon
              </span>
            </h1>

            <div className="space-y-3">
              <h2 className="heading-responsive font-semibold bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
                Design. Code. Inspire.
              </h2>
              <p className="text-responsive text-slate-400 font-light tracking-wide">
                Minimalist. Maker. Dreamer.
              </p>
            </div>
          </div>

          {/* Premium Role Badge */}
          <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 text-white font-semibold shadow-lg text-sm md:text-base tracking-widest uppercase touch-target" data-animate>
            <i className="fas fa-rocket"></i>
            Founder, Prologware & Techdio
          </div>

          {/* Premium Description */}
          <p className="text-responsive text-slate-300 max-w-2xl mx-auto leading-relaxed font-light" data-animate>
            I craft digital experiences that blend
            <span className="font-semibold text-indigo-400"> clarity</span> and
            <span className="font-semibold text-blue-400"> creativity</span>.
            <br className="hidden sm:block" />
            From code to concept, I believe in less, but better.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4" data-animate>
            <a 
              href="/images/Resume.pdf" 
              target="_blank" 
              className="btn btn-primary btn-lg group touch-target" 
              aria-label="Download Abdul Basit Memon's Resume"
            >
              <i className="fas fa-download"></i>
              <span>Download Resume</span>
              <i className="fas fa-arrow-right group-hover:translate-x-1 transition-transform"></i>
            </a>
            <a 
              href="#contact" 
              className="btn btn-secondary btn-lg group touch-target" 
              aria-label="Contact Abdul Basit Memon"
            >
              <span className="text-2xl">💬</span> Let's Connect
              <i className="fas fa-arrow-right group-hover:translate-x-1 transition-transform"></i>
            </a>
          </div>

          {/* Premium Skills Badges */}
          <div className="flex flex-wrap gap-3 justify-center pt-8" data-animate>
            <span className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-slate-800 text-indigo-400 font-medium shadow-md border border-indigo-500/30 text-sm md:text-base">
              <i className="fas fa-code"></i>Full Stack
            </span>
            <span className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-slate-800 text-purple-400 font-medium shadow-md border border-purple-500/30 text-sm md:text-base">
              <i className="fas fa-lightbulb"></i>Creative
            </span>
            <span className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-slate-800 text-blue-400 font-medium shadow-md border border-blue-500/30 text-sm md:text-base">
              <i className="fas fa-rocket"></i>Startup
            </span>
            <span className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-slate-800 text-indigo-300 font-medium shadow-md border border-indigo-400/30 text-sm md:text-base">
              <i className="fas fa-graduation-cap"></i>Educator
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
