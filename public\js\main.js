document.addEventListener('DOMContentLoaded', () => {
// Smooth Scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

// Contact Form Validation
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(event) {
            event.preventDefault();
            let isValid = true;

            // Reset validation states
            document.querySelectorAll('#contactForm .is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });
            document.querySelectorAll('#contactForm .invalid-feedback').forEach(el => {
                el.style.display = 'none';
            });

            // Validate Full Name
            const fullName = document.getElementById('fullName');
            if (!fullName.value) {
                isValid = false;
                fullName.classList.add('is-invalid');
                fullName.nextElementSibling.style.display = 'block';
            }

            // Validate Email
            const email = document.getElementById('email');
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!email.value || !emailPattern.test(email.value)) {
                isValid = false;
                email.classList.add('is-invalid');
                email.nextElementSibling.style.display = 'block';
            }

            // Validate Message
            const message = document.getElementById('message');
            if (!message.value) {
                isValid = false;
                message.classList.add('is-invalid');
                message.nextElementSibling.style.display = 'block';
            }

            // Button UI logic
            const sendBtn = contactForm.querySelector('button[type="submit"]');
            if (sendBtn) {
                sendBtn.disabled = true;
                sendBtn.innerHTML = '<span class="spinner-border animate-spin inline-block w-5 h-5 mr-2 border-2 border-white border-t-transparent rounded-full align-middle"></span>Sending...';
            }

            if (isValid) {
                const formData = new FormData(contactForm);
                fetch(contactForm.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        Swal.fire({
                            title: 'Success!',
                            text: 'Your message has been sent.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            contactForm.reset();
                        });
                    } else {
                        response.text().then(text => {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Something went wrong. ' + text,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        });
                    }
                })
                .catch(error => {
                    Swal.fire({
                        title: 'Error!',
                        text: 'There was a problem with your submission.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                })
                .finally(() => {
                    if (sendBtn) {
                        sendBtn.disabled = false;
                        sendBtn.innerHTML = 'Send Message';
                    }
                });
            } else {
                if (sendBtn) {
                    sendBtn.disabled = false;
                    sendBtn.innerHTML = 'Send Message';
                }
            }
        });
    }

});