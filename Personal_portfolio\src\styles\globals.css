/* Additional Global Styles and Animations */

/* Responsive Typography */
@layer utilities {
  .title-responsive {
    font-size: clamp(2.5rem, 8vw, 4rem);
  }
  
  .heading-responsive {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
  }
  
  .text-responsive {
    font-size: clamp(1rem, 2vw, 1.25rem);
  }
}

/* Custom Animations */
@keyframes wave {
  0% { transform: rotate(0deg); }
  10% { transform: rotate(14deg); }
  20% { transform: rotate(-8deg); }
  30% { transform: rotate(14deg); }
  40% { transform: rotate(-4deg); }
  50% { transform: rotate(10deg); }
  60% { transform: rotate(0deg); }
  100% { transform: rotate(0deg); }
}

.animate-wave {
  animation: wave 1.2s cubic-bezier(.36,.07,.19,.97) both;
  transform-origin: 70% 70%;
}

/* Background Animations */
@keyframes pulse-slow {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.08); }
}

.animate-pulse-slow {
  animation: pulse-slow 6s ease-in-out infinite;
}

@keyframes pulse-slow2 {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.12); }
}

.animate-pulse-slow2 {
  animation: pulse-slow2 8s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-30px); }
}

.animate-float {
  animation: float 7s ease-in-out infinite;
}

/* Navigation Styles */
.nav-link {
  color: rgb(209 213 219);
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.2s;
  position: relative;
  padding: 0.5rem 0;
}

.nav-link:hover {
  color: white;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: rgb(99 102 241);
  transition: width 0.2s;
}

.nav-link:hover::after {
  width: 100%;
}

.mobile-link {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  color: rgb(209 213 219);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  border-radius: 0.5rem;
}

.mobile-link:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.05);
}

/* Project Filter Buttons */
.category-filter {
  @apply px-6 py-3 rounded-full font-medium text-sm transition-all duration-300 border border-indigo-500/30 text-indigo-300 hover:text-white hover:bg-indigo-500/20;
}

.category-filter.active {
  @apply bg-gradient-to-r from-indigo-500 to-blue-500 text-white border-transparent shadow-lg;
}

/* Footer Styles */
.footer-premium {
  background: linear-gradient(120deg, var(--glass-bg) 60%, rgba(99,102,241,0.05) 100%);
  border-top: 2px solid var(--border-color);
  box-shadow: 0 -8px 32px 0 var(--shadow-primary), 0 -1.5px 6px 0 var(--shadow-secondary);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.footer-glow {
  position: absolute;
  top: -60px;
  left: 50%;
  width: 400px;
  height: 120px;
  background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
  opacity: 0.18;
  filter: blur(16px);
  transform: translateX(-50%);
  pointer-events: none;
  z-index: 0;
}

.footer-content {
  position: relative;
  z-index: 1;
}

.footer-link {
  transition: color 0.2s, text-shadow 0.2s, transform 0.2s;
  text-shadow: 0 2px 8px rgba(129,140,248,0.10);
}

.footer-link:hover {
  color: var(--primary-color);
  text-shadow: 0 4px 16px var(--primary-color), 0 1.5px 6px #3b82f6;
  transform: scale(1.08) translateY(-2px);
}

.footer-social {
  transition: transform 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(129,140,248,0.10);
  border-radius: 50%;
  background: rgba(24,28,43,0.7);
  padding: 0.5rem;
  margin: 0 0.25rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.footer-social:hover {
  background: var(--primary-color);
  color: #fff !important;
  transform: scale(1.15) rotate(-6deg);
  box-shadow: 0 4px 16px var(--primary-color);
}

.footer-divider {
  border-color: var(--primary-color);
  opacity: 0.18;
}

.footer-copyright {
  color: var(--secondary-color);
  font-size: 0.95rem;
  letter-spacing: 0.02em;
  text-align: center;
}

/* Scroll Animations */
[data-animate] {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.loaded [data-animate] {
  opacity: 1;
  transform: translateY(0);
}

/* Loading States */
.loading {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.loaded {
  opacity: 1;
}

/* Project Cards */
.project-item {
  transition: all 0.3s ease;
}

.project-item.hidden {
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
}

.project-thumb {
  transition: all 0.5s ease;
}

.project-item:hover .project-thumb {
  transform: scale(1.05);
}

/* Form Styles */
.form-input {
  @apply block w-full px-5 py-3 bg-gradient-to-r from-slate-800/60 to-slate-900/60 border border-indigo-500/30 rounded-xl text-white placeholder:text-slate-400 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300 shadow-inner;
}

.form-label {
  @apply block text-base font-semibold text-indigo-400 mb-2 tracking-wide;
}

/* Utility Classes */
.text-gradient {
  @apply bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 bg-clip-text text-transparent;
}

.bg-glass {
  background: rgba(15, 23, 42, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
}

.border-glow {
  border: 1px solid rgba(99, 102, 241, 0.5);
  box-shadow: 0 0 10px rgba(99, 102, 241, 0.2);
}
