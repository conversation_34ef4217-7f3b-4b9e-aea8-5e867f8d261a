import { useEffect } from 'react';

export const useIntersectionObserver = (selector = '[data-animate]', options = {}) => {
  useEffect(() => {
    const defaultOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px',
      ...options
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '1';
          entry.target.style.transform = 'translateY(0)';
          entry.target.classList.add('animate-in');
        }
      });
    }, defaultOptions);

    // Observe elements
    const elements = document.querySelectorAll(selector);
    elements.forEach(el => {
      // Set initial state
      el.style.opacity = '0';
      el.style.transform = 'translateY(20px)';
      el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
      
      observer.observe(el);
    });

    // Cleanup
    return () => {
      observer.disconnect();
    };
  }, [selector, options]);
};
