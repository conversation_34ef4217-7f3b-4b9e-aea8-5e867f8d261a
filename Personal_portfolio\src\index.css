@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

@theme {
  --font-poppins: 'Poppins', sans-serif;

  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-primary-color: #6366f1;
  --color-primary-light: #818cf8;
  --color-primary-dark: #4f46e5;
  --color-secondary-color: #06b6d4;
  --color-secondary-light: #67e8f9;
  --color-accent-color: #8b5cf6;
  --color-accent-light: #a78bfa;
  --color-text-primary: #f8fafc;
  --color-text-secondary: #e2e8f0;
  --color-text-muted: #94a3b8;

  --animate-pulse-slow: pulse-slow 6s ease-in-out infinite;
  --animate-pulse-slow2: pulse-slow2 8s ease-in-out infinite;
  --animate-float: float 7s ease-in-out infinite;
  --animate-wave: wave 1.2s cubic-bezier(.36,.07,.19,.97) both;
}

/* Custom CSS Variables for compatibility */
:root {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --primary-color: #6366f1;
  --primary-light: #818cf8;
  --primary-dark: #4f46e5;
  --secondary-color: #06b6d4;
  --secondary-light: #67e8f9;
  --accent-color: #8b5cf6;
  --accent-light: #a78bfa;
  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-muted: #94a3b8;
  --border-color: rgba(99, 102, 241, 0.2);
  --card-bg: rgba(30, 41, 59, 0.8);
  --glass-bg: rgba(15, 23, 42, 0.9);
  --shadow-primary: rgba(99, 102, 241, 0.25);
  --shadow-secondary: rgba(6, 182, 212, 0.15);
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
  color: var(--text-primary);
  overflow-x: hidden;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
}

/* Custom Component Classes */
@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-heading {
    @apply text-4xl md:text-5xl font-bold text-center bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 bg-clip-text text-transparent mb-8;
  }

  .card {
    @apply relative bg-gradient-to-br from-slate-800/90 via-slate-800/80 to-slate-900/80 rounded-3xl p-8 shadow-2xl border border-indigo-500/10 backdrop-blur-md overflow-hidden transition-all duration-300 hover:-translate-y-2;
  }

  .glass-card {
    @apply relative bg-gradient-to-br from-slate-800/60 via-slate-800/40 to-slate-900/60 rounded-3xl p-8 shadow-2xl border border-indigo-500/20 backdrop-blur-lg overflow-hidden;
  }

  .btn {
    @apply inline-flex items-center gap-2 px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-indigo-600 to-blue-600 text-white hover:from-indigo-700 hover:to-blue-700 shadow-lg hover:shadow-xl focus:ring-indigo-500;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-slate-700 to-slate-800 text-white hover:from-slate-600 hover:to-slate-700 shadow-lg hover:shadow-xl focus:ring-slate-500;
  }

  .btn-lg {
    @apply px-8 py-4 text-lg;
  }

  .premium-btn {
    @apply relative overflow-hidden;
  }

  .btn-glow {
    @apply absolute inset-0 bg-gradient-to-r from-indigo-500/20 via-blue-500/20 to-purple-500/20 rounded-full blur opacity-0 transition-opacity duration-300 group-hover:opacity-100;
  }

  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
}
