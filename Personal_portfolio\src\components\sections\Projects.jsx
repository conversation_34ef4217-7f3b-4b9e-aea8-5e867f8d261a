import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

const Projects = () => {
  const [filter, setFilter] = useState('all');
  const [visibleProjects, setVisibleProjects] = useState([]);

  const featuredProjects = [
    {
      title: "TechdioApp",
      description: "A sleek, cross-platform learning hub on .NET MAUI. Browse curated tech courses, book one-on-one tutor sessions, and track your progress with offline-capable data syncing.",
      technologies: [".NET MAUI", "C#", "SQLite", "XAML"],
      icon: "fas fa-graduation-cap",
      gradient: "from-purple-500/80 to-indigo-400/80",
      year: "2024",
      category: "SOFTWARE",
      demoLink: "#",
      githubLink: "https://github.com/abm1119/TechdioApp"
    },
    {
      title: "Carpooling App",
      description: "A comprehensive carpooling solution built with React Native, featuring real-time ride matching, secure payments, and GPS tracking for seamless shared transportation.",
      technologies: ["React Native", "Firebase", "Maps API", "Payment Gateway"],
      icon: "fas fa-car",
      gradient: "from-indigo-500/80 to-blue-400/80",
      year: "2024",
      category: "MOBILE",
      demoLink: "https://vimeo.com/1102623700",
      githubLink: "https://github.com/urooj-marvi/Carpooling_App"
    },
    {
      title: "Arduino Speed Detector",
      description: "An intelligent speed detection system using Arduino and ultrasonic sensors. Features real-time monitoring, data logging, and alert mechanisms for traffic management.",
      technologies: ["Arduino", "C++", "Sensors", "IoT"],
      icon: "fas fa-microchip",
      gradient: "from-green-500/80 to-blue-400/80",
      year: "2024",
      category: "HARDWARE",
      demoLink: "#",
      githubLink: "https://github.com/abm1119/Arduino-Vehicle-Speed-Detector-"
    },
    {
      title: "AI Chat Assistant",
      description: "An intelligent chatbot powered by OpenAI's GPT API with custom training data, context awareness, and multi-language support for enhanced user interactions.",
      technologies: ["Python", "OpenAI API", "Flask", "React"],
      icon: "fas fa-robot",
      gradient: "from-purple-500/80 to-pink-400/80",
      year: "2024",
      category: "AI/ML",
      demoLink: "#",
      githubLink: "#"
    },
    {
      title: "E-Commerce Platform",
      description: "Full-stack e-commerce solution with payment integration, inventory management, and admin dashboard built with modern web technologies.",
      technologies: ["React", "Node.js", "MongoDB", "Stripe"],
      icon: "fas fa-shopping-cart",
      gradient: "from-emerald-500/80 to-teal-400/80",
      year: "2023",
      category: "WEB",
      demoLink: "#",
      githubLink: "#"
    },
    {
      title: "IoT Home Automation",
      description: "Smart home system with ESP32, sensors, and mobile app control for lighting, temperature, and security monitoring with real-time notifications.",
      technologies: ["ESP32", "Arduino IDE", "Firebase", "Flutter"],
      icon: "fas fa-home",
      gradient: "from-orange-500/80 to-red-400/80",
      year: "2023",
      category: "HARDWARE",
      demoLink: "#",
      githubLink: "#"
    }
  ];

  const categories = [
    { id: 'all', label: 'All Projects', icon: 'fas fa-th-large', count: featuredProjects.length },
    { id: 'SOFTWARE', label: 'Software', icon: 'fas fa-laptop-code', count: featuredProjects.filter(p => p.category === 'SOFTWARE').length },
    { id: 'MOBILE', label: 'Mobile', icon: 'fas fa-mobile-alt', count: featuredProjects.filter(p => p.category === 'MOBILE').length },
    { id: 'HARDWARE', label: 'Hardware', icon: 'fas fa-microchip', count: featuredProjects.filter(p => p.category === 'HARDWARE').length },
    { id: 'WEB', label: 'Web', icon: 'fas fa-globe', count: featuredProjects.filter(p => p.category === 'WEB').length },
    { id: 'AI/ML', label: 'AI/ML', icon: 'fas fa-brain', count: featuredProjects.filter(p => p.category === 'AI/ML').length }
  ];

  const filteredProjects = filter === 'all' ? featuredProjects : featuredProjects.filter(project => project.category === filter);

  useEffect(() => {
    setVisibleProjects(filteredProjects);
  }, [filter]);

  const handleFilterChange = (newFilter) => {
    setVisibleProjects([]);
    setTimeout(() => {
      setFilter(newFilter);
    }, 150);
  };

  return (
    <section id="projects" className="py-24 bg-gradient-to-b from-bg-primary via-bg-secondary/60 to-bg-primary relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-primary-color/10 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-tr from-secondary-color/8 to-transparent rounded-full blur-3xl animate-pulse-slow2"></div>
      </div>

      <div className="container mx-auto relative z-10">
        <div className="max-w-4xl mx-auto text-center mb-20">
          <h2 className="section-heading mb-6" data-animate>Featured Projects</h2>
          <div className="mx-auto w-32 h-1 bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 rounded-full mb-8"></div>
          <p className="text-xl text-slate-300 font-light leading-relaxed">
            A curated showcase of my most impactful and creative work. Each project represents innovation,
            problem-solving, and commitment to excellence in software development.
          </p>
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-16" data-animate>
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleFilterChange(category.id)}
              className={`flex items-center gap-3 px-6 py-3 rounded-full font-medium transition-all duration-300 border ${
                filter === category.id
                  ? 'bg-gradient-to-r from-indigo-500 to-blue-500 text-white border-transparent shadow-lg scale-105'
                  : 'border-slate-600/50 text-slate-400 hover:text-white hover:border-slate-500/70 hover:bg-slate-800/50'
              }`}
            >
              <i className={`${category.icon} text-lg`}></i>
              <span className="hidden sm:inline">{category.label}</span>
              <span className={`text-xs px-2 py-1 rounded-full ${
                filter === category.id ? 'bg-white/20' : 'bg-slate-700/50'
              }`}>
                {category.count}
              </span>
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {visibleProjects.map((project, index) => (
            <div
              key={`${project.title}-${index}`}
              className="group relative bg-gradient-to-br from-slate-800/90 via-slate-800/80 to-slate-900/90 rounded-3xl overflow-hidden shadow-2xl border border-indigo-500/10 backdrop-blur-md transition-all duration-500 hover:-translate-y-3 hover:shadow-3xl animate-in"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              {/* Animated Background */}
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-purple-400/20 via-indigo-400/10 to-blue-400/0 rounded-full blur-2xl opacity-30 group-hover:opacity-60 transition-opacity duration-500"></div>

              {/* Project Thumbnail */}
              <div className={`relative bg-gradient-to-br ${project.gradient} flex items-center justify-center h-48 shadow-2xl backdrop-blur-lg overflow-hidden`}>
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                <i className={`${project.icon} fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 relative z-10`}></i>

                {/* Floating particles effect */}
                <div className="absolute top-4 left-4 w-2 h-2 bg-white/30 rounded-full animate-pulse"></div>
                <div className="absolute bottom-6 right-6 w-1 h-1 bg-white/40 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                <div className="absolute top-1/2 left-6 w-1.5 h-1.5 bg-white/20 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
              </div>

              {/* Project Content */}
              <div className="p-8 relative z-10">
                {/* Project Meta */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <span className="text-xs font-medium text-indigo-400 bg-indigo-400/10 px-3 py-1 rounded-full border border-indigo-400/20">{project.year}</span>
                    <span className="text-xs font-medium text-purple-400 bg-purple-400/10 px-3 py-1 rounded-full border border-purple-400/20">{project.category}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-xs text-slate-400">Active</span>
                  </div>
                </div>

                {/* Project Title */}
                <h3 className="font-bold text-2xl mb-4 text-white group-hover:text-indigo-300 transition-colors duration-300">
                  {project.title}
                </h3>

                {/* Project Description */}
                <p className="mb-6 text-slate-300 group-hover:text-slate-200 text-sm leading-relaxed line-clamp-3">
                  {project.description}
                </p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2 mb-8">
                  {project.technologies.map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="px-3 py-1 text-xs font-medium bg-slate-700/50 text-slate-300 rounded-full border border-slate-600/30 group-hover:bg-indigo-500/20 group-hover:border-indigo-400/30 group-hover:text-indigo-300 transition-all duration-300"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-3">
                  <a
                    href={project.demoLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 bg-gradient-to-r from-indigo-600 to-blue-600 text-white py-3 px-6 rounded-xl font-semibold text-center hover:from-indigo-700 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl group/btn"
                  >
                    <span className="flex items-center justify-center gap-2">
                      <i className="fas fa-external-link-alt opacity-70 group-hover/btn:opacity-100 transition-opacity"></i>
                      <span>View Demo</span>
                    </span>
                  </a>
                  <a
                    href={project.githubLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-4 py-3 rounded-xl border border-indigo-500/30 text-indigo-400 hover:bg-indigo-500/10 hover:border-indigo-400/50 transition-all duration-300 group/github"
                  >
                    <i className="fab fa-github text-lg group-hover/github:scale-110 transition-transform"></i>
                  </a>
                </div>
              </div>

              {/* Hover Glow Effect */}
              <div className="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                <div className="absolute inset-0 rounded-3xl shadow-2xl shadow-indigo-500/20"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {visibleProjects.length === 0 && (
          <div className="text-center py-20">
            <div className="w-24 h-24 bg-slate-800/50 rounded-full flex items-center justify-center mx-auto mb-6">
              <i className="fas fa-search text-3xl text-slate-400"></i>
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">No Projects Found</h3>
            <p className="text-slate-400 max-w-md mx-auto">
              No projects match the selected category. Try selecting a different filter or check back later for new projects.
            </p>
          </div>
        )}
        
        <div className="flex justify-center mt-20">
          <Link to="/projects" className="relative premium-btn py-5 px-12 text-lg font-semibold flex items-center gap-3 shadow-xl border border-primary-color/40 rounded-full bg-gradient-to-r from-slate-800 via-slate-900 to-slate-800 hover:bg-gradient-to-r hover:from-indigo-500 hover:via-blue-500 hover:to-purple-500 transition-all duration-300 group">
            <span className="text-2xl">🚀</span>
            <span className="tracking-wide font-semibold">View More Projects</span>
            <span className="btn-glow"></span>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Projects;
