import React from 'react';
import { Link } from 'react-router-dom';

const Projects = () => {
  const featuredProjects = [
    {
      title: "TechdioApp",
      description: "A sleek, cross-platform learning hub on .NET MAUI. Browse curated tech courses, book one-on-one tutor sessions, and track your progress with offline-capable data syncing.",
      technologies: [".NET MAUI", "C#", "SQLite", "XAML"],
      icon: "fas fa-graduation-cap",
      gradient: "from-purple-500/80 to-indigo-400/80",
      year: "2024",
      category: "SOFTWARE",
      demoLink: "#",
      githubLink: "https://github.com/abm1119/TechdioApp"
    },
    {
      title: "Carpooling App",
      description: "A comprehensive carpooling solution built with React Native, featuring real-time ride matching, secure payments, and GPS tracking for seamless shared transportation.",
      technologies: ["React Native", "Firebase", "Maps API", "Payment Gateway"],
      icon: "fas fa-car",
      gradient: "from-indigo-500/80 to-blue-400/80",
      year: "2024",
      category: "MOBILE",
      demoLink: "https://vimeo.com/1102623700",
      githubLink: "https://github.com/urooj-marvi/Carpooling_App"
    },
    {
      title: "Arduino Speed Detector",
      description: "An intelligent speed detection system using Arduino and ultrasonic sensors. Features real-time monitoring, data logging, and alert mechanisms for traffic management.",
      technologies: ["Arduino", "C++", "Sensors", "IoT"],
      icon: "fas fa-microchip",
      gradient: "from-green-500/80 to-blue-400/80",
      year: "2024",
      category: "HARDWARE",
      demoLink: "#",
      githubLink: "#"
    }
  ];

  return (
    <section id="projects" className="py-16 md:py-24 bg-gradient-to-b from-bg-primary via-bg-secondary/60 to-bg-primary">
      <div className="container mx-auto">
        <div className="max-w-3xl mx-auto text-center mb-16 md:mb-20">
          <h2 className="section-heading mb-6" data-animate>My Projects</h2>
          <div className="mx-auto w-24 h-1 bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 rounded-full mb-6"></div>
          <p className="text-lg md:text-xl text-slate-300 font-light leading-relaxed mb-2">
            A curated showcase of my most impactful and creative work. Each project blends thoughtful engineering, design, and real-world value.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12 max-w-7xl mx-auto">
          {featuredProjects.map((project, index) => (
            <div key={index} className="group card relative overflow-hidden backdrop-blur-sm" data-aos="fade-up" data-aos-delay={index * 200}>
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
              <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-purple-400/20 via-indigo-400/10 to-blue-400/0 rounded-full blur-2xl opacity-30 z-0"></div>
              
              <div className={`project-thumb bg-gradient-to-br ${project.gradient} flex items-center justify-center h-48 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg`}>
                <i className={`${project.icon} fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110`}></i>
              </div>
              
              <div className="p-8 relative z-10">
                <div className="flex items-center gap-3 mb-3">
                  <span className="text-xs font-medium text-purple-300/80">{project.year}</span>
                  <span className="w-1 h-1 bg-purple-300/50 rounded-full"></span>
                  <span className="text-xs font-medium text-purple-300/80">{project.category}</span>
                </div>
                <h3 className="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">{project.title}</h3>
                <p className="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">{project.description}</p>
                
                <div className="flex flex-wrap gap-2 mb-6">
                  {project.technologies.map((tech, techIndex) => (
                    <span key={techIndex} className="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">{tech}</span>
                  ))}
                </div>
                
                <div className="flex items-center gap-3">
                  <a href={project.demoLink} target="_blank" rel="noopener noreferrer" className="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                    <span className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-purple-500/30 group-hover/btn:to-indigo-500/30"></span>
                    <span className="relative flex items-center justify-center">
                      <i className="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                      View Project
                    </span>
                  </a>
                  <a href={project.githubLink} target="_blank" rel="noopener noreferrer" className="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                    <i className="fab fa-github fa-lg"></i>
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="flex justify-center mt-20">
          <Link to="/projects" className="relative premium-btn py-5 px-12 text-lg font-semibold flex items-center gap-3 shadow-xl border border-primary-color/40 rounded-full bg-gradient-to-r from-slate-800 via-slate-900 to-slate-800 hover:bg-gradient-to-r hover:from-indigo-500 hover:via-blue-500 hover:to-purple-500 transition-all duration-300 group">
            <span className="text-2xl">🚀</span>
            <span className="tracking-wide font-semibold">View More Projects</span>
            <span className="btn-glow"></span>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Projects;
