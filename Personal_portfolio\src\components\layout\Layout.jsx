import React, { useEffect, useState } from 'react';
import Navigation from './Navigation';
import Footer from './Footer';
import ScrollProgress from '../ui/ScrollProgress';
import { PageLoader } from '../ui/LoadingSpinner';

const Layout = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time and wait for critical resources
    const timer = setTimeout(() => {
      setIsLoading(false);
      document.body.classList.add('loaded');

      // Fade in elements with data-animate attribute
      setTimeout(() => {
        document.querySelectorAll('[data-animate]').forEach(el => {
          el.style.opacity = '1';
          el.style.transform = 'translateY(0)';
        });
      }, 100);
    }, 1500);

    return () => {
      clearTimeout(timer);
      document.body.classList.remove('loaded');
    };
  }, []);

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary">
      <ScrollProgress />
      <Navigation />
      <main style={{ paddingTop: '80px' }}>
        {children}
      </main>
      <Footer />
    </div>
  );
};

export default Layout;
