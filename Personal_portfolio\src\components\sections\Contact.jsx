import React, { useState } from 'react';

const Contact = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Form submission logic will be implemented later
    setTimeout(() => {
      setIsSubmitting(false);
      alert('Message sent successfully!');
      setFormData({ fullName: '', email: '', subject: '', message: '' });
    }, 2000);
  };

  return (
    <section id="contact" className="py-16 md:py-24">
      <div className="container mx-auto">
        <h2 className="section-heading text-center mb-12 md:mb-16" data-animate>Contact Me</h2>
        <div className="lg:flex lg:items-center lg:-mx-6">
          <div className="lg:w-1/2 lg:mx-6">
            <div className="relative bg-gradient-to-br from-slate-800/90 via-slate-800/80 to-slate-900/80 rounded-3xl p-12 shadow-2xl border border-primary-color/10 backdrop-blur-md overflow-hidden">
              <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-indigo-500/30 via-blue-500/20 to-purple-500/10 rounded-full blur-2xl opacity-40 -z-10"></div>
              <div className="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tr from-purple-500/20 via-indigo-400/10 to-blue-500/10 rounded-full blur-2xl opacity-30 -z-10"></div>
              
              <h1 className="text-4xl font-extrabold text-white capitalize lg:text-5xl mb-6 tracking-tight flex items-center gap-3">
                <svg className="w-8 h-8 text-primary-color" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M8 10h.01M12 10h.01M16 10h.01M21 16v-5a2 2 0 00-2-2h-1.26A8 8 0 103 15.25"></path>
                </svg>
                Let's Work Together
              </h1>
              <p className="text-slate-200 text-lg md:text-xl font-medium leading-relaxed mb-8">Want to build something meaningful? Have a crazy idea to test? Need help with a project?</p>
              
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <i className="fas fa-envelope text-2xl text-primary-color"></i>
                  <a href="mailto:<EMAIL>" className="text-lg md:text-xl font-semibold text-primary-color hover:underline transition"><EMAIL></a>
                </div>
                <div className="flex items-center gap-4">
                  <i className="fab fa-whatsapp text-3xl text-green-500"></i>
                  <a href="https://wa.me/923160360531" className="text-lg md:text-xl font-semibold text-green-400 hover:underline transition flex items-center gap-2">
                    <span>+92 316 036-0531</span>
                  </a>
                </div>
                <div className="flex items-center gap-4">
                  <i className="fas fa-map-marker-alt text-2xl text-primary-color"></i>
                  <span className="text-lg md:text-xl font-semibold text-slate-200">Based in Khairpur Mirs, Sindh – working globally</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-12 lg:mt-0 lg:w-1/2 lg:mx-6">
            <div className="card p-8">
              <h2 className="text-2xl md:text-3xl font-bold text-white mb-7 tracking-tight font-poppins flex items-center gap-2">
                <svg className="w-6 h-6 text-primary-color" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M21 10.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l2.9-5.7a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6A8.38 8.38 0 0113.5 3a8.5 8.5 0 017.5 7.5z"></path>
                </svg>
                Send a Message
              </h2>
              
              <form onSubmit={handleSubmit} className="mt-6">
                <div className="flex flex-col space-y-7">
                  <div>
                    <label htmlFor="fullName" className="form-label">Full Name</label>
                    <input 
                      type="text" 
                      id="fullName" 
                      name="fullName" 
                      value={formData.fullName}
                      onChange={handleChange}
                      className="form-input" 
                      placeholder="Your Full Name" 
                      required 
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="form-label">Email Address</label>
                    <input 
                      type="email" 
                      id="email" 
                      name="email" 
                      value={formData.email}
                      onChange={handleChange}
                      className="form-input" 
                      placeholder="Your Email Address" 
                      required 
                    />
                  </div>
                  <div>
                    <label htmlFor="subject" className="form-label">Subject</label>
                    <input 
                      type="text" 
                      id="subject" 
                      name="subject" 
                      value={formData.subject}
                      onChange={handleChange}
                      className="form-input" 
                      placeholder="Your Subject" 
                      required 
                    />
                  </div>
                  <div>
                    <label htmlFor="message" className="form-label">Message</label>
                    <textarea 
                      id="message" 
                      name="message" 
                      rows="4" 
                      value={formData.message}
                      onChange={handleChange}
                      className="form-input" 
                      placeholder="Your message here" 
                      required
                    ></textarea>
                  </div>
                  <button 
                    type="submit" 
                    disabled={isSubmitting}
                    className="relative premium-btn w-full py-4 px-8 text-lg font-semibold flex items-center justify-center gap-3 shadow-xl border border-primary-color/40 rounded-full bg-gradient-to-r from-slate-800 via-slate-900 to-slate-800 hover:bg-gradient-to-r hover:from-indigo-500 hover:via-blue-500 hover:to-purple-500 transition-all duration-300 group overflow-hidden disabled:opacity-50"
                  >
                    <span className="z-10 relative">
                      {isSubmitting ? 'Sending...' : 'Send Message'}
                    </span>
                    <span className="btn-glow"></span>
                    <svg className="w-6 h-6 ml-2 text-primary-color group-hover:text-white transition" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M22 2L11 13"></path>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M22 2l-7 20-4-9-9-4 20-7z"></path>
                    </svg>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
