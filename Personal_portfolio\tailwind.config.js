/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'poppins': ['Poppins', 'sans-serif'],
      },
      colors: {
        'bg-primary': '#0f172a',
        'bg-secondary': '#1e293b',
        'bg-tertiary': '#334155',
        'primary-color': '#6366f1',
        'primary-light': '#818cf8',
        'primary-dark': '#4f46e5',
        'secondary-color': '#06b6d4',
        'secondary-light': '#67e8f9',
        'accent-color': '#8b5cf6',
        'accent-light': '#a78bfa',
        'text-primary': '#f8fafc',
        'text-secondary': '#e2e8f0',
        'text-muted': '#94a3b8',
      },
      animation: {
        'pulse-slow': 'pulse-slow 6s ease-in-out infinite',
        'pulse-slow2': 'pulse-slow2 8s ease-in-out infinite',
        'float': 'float 7s ease-in-out infinite',
        'wave': 'wave 1.2s cubic-bezier(.36,.07,.19,.97) both',
      },
      keyframes: {
        'pulse-slow': {
          '0%, 100%': { opacity: '0.4', transform: 'scale(1)' },
          '50%': { opacity: '0.7', transform: 'scale(1.08)' },
        },
        'pulse-slow2': {
          '0%, 100%': { opacity: '0.3', transform: 'scale(1)' },
          '50%': { opacity: '0.6', transform: 'scale(1.12)' },
        },
        'float': {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-30px)' },
        },
        'wave': {
          '0%': { transform: 'rotate(0deg)' },
          '10%': { transform: 'rotate(14deg)' },
          '20%': { transform: 'rotate(-8deg)' },
          '30%': { transform: 'rotate(14deg)' },
          '40%': { transform: 'rotate(-4deg)' },
          '50%': { transform: 'rotate(10deg)' },
          '60%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(0deg)' },
        },
      },
      backdropBlur: {
        'xs': '2px',
      },
      fontSize: {
        'title-responsive': 'clamp(2.5rem, 8vw, 4rem)',
        'heading-responsive': 'clamp(1.5rem, 4vw, 2.5rem)',
        'text-responsive': 'clamp(1rem, 2vw, 1.25rem)',
      },
    },
  },
  plugins: [],
}
