import React from 'react';

const Skills = () => {
  const skillCategories = [
    {
      title: "Programming Languages",
      icon: "fas fa-code",
      color: "indigo",
      skills: [
        { name: "C++", icon: "fa-brands fa-cuttlefish", color: "indigo" },
        { name: "Java", icon: "fa-brands fa-java", color: "blue" },
        { name: "Python", icon: "fa-brands fa-python", color: "yellow" },
        { name: "C#", icon: "fa-solid fa-hashtag", color: "green" }
      ]
    },
    {
      title: "Web Development",
      icon: "fas fa-globe",
      color: "blue",
      skills: [
        { name: "HTML/CSS", icon: "fa-brands fa-html5", color: "pink" },
        { name: "JavaScript", icon: "fa-brands fa-js", color: "yellow" },
        { name: "ASP.NET", icon: "fa-brands fa-microsoft", color: "blue" },
        { name: "Tailwind CSS", icon: "fa-brands fa-css3-alt", color: "indigo" },
        { name: "React (Learning)", icon: "fa-brands fa-react", color: "purple" }
      ]
    },
    {
      title: "Mobile Development",
      icon: "fas fa-mobile-alt",
      color: "purple",
      skills: [
        { name: ".NET MAUI", icon: "fa-brands fa-microsoft", color: "purple" },
        { name: "C#", icon: "fa-solid fa-hashtag", color: "green" },
        { name: "XML", icon: "fa-solid fa-code", color: "pink" },
        { name: "Visual Studio", icon: "fa-brands fa-windows", color: "blue" },
        { name: "Java", icon: "fa-brands fa-java", color: "yellow" },
        { name: "Android Studio", icon: "fa-brands fa-android", color: "green" }
      ]
    },
    {
      title: "Databases",
      icon: "fas fa-database",
      color: "green",
      skills: [
        { name: "Firebase", icon: "fa-brands fa-google", color: "green" },
        { name: "MySQL", icon: "fa-solid fa-database", color: "yellow" },
        { name: "Supabase", icon: "fa-solid fa-database", color: "blue" },
        { name: "PostgreSQL", icon: "fa-solid fa-database", color: "purple" },
        { name: "SSMS", icon: "fa-solid fa-database", color: "green" }
      ]
    },
    {
      title: "Tools, Software & Platforms",
      icon: "fas fa-tools",
      color: "pink",
      skills: [
        { name: "MATLAB", icon: "fa-solid fa-calculator", color: "pink" },
        { name: "Git/GitHub", icon: "fa-brands fa-git-alt", color: "gray" },
        { name: "Notion", icon: "fa-solid fa-note-sticky", color: "yellow" },
        { name: "Photoshop", icon: "fa-brands fa-adobe", color: "blue" },
        { name: "NetBeans", icon: "fa-brands fa-java", color: "green" },
        { name: "Xilinx Vivado", icon: "fa-solid fa-microchip", color: "purple" },
        { name: "LabVIEW", icon: "fa-solid fa-project-diagram", color: "pink" },
        { name: "Jupyter Notebooks", icon: "fa-solid fa-book", color: "yellow" },
        { name: "Office Suite", icon: "fa-solid fa-file-word", color: "blue" },
        { name: "MIT Scratch", icon: "fa-solid fa-brain", color: "pink" }
      ]
    },
    {
      title: "Hardware & IoT",
      icon: "fas fa-microchip",
      color: "yellow",
      skills: [
        { name: "Arduino UNO", icon: "fa-brands fa-usb", color: "yellow" },
        { name: "Arduino IDE", icon: "fa-solid fa-code", color: "blue" },
        { name: "ESP32/ESP8266", icon: "fa-solid fa-wifi", color: "green" },
        { name: "Raspberry Pi", icon: "fa-brands fa-raspberry-pi", color: "pink" },
        { name: "PictoBlox", icon: "fa-solid fa-cube", color: "yellow" }
      ]
    },
    {
      title: "Soft Skills",
      icon: "fas fa-users",
      color: "green",
      skills: [
        { name: "Problem-solving", icon: "fa-solid fa-lightbulb", color: "green" },
        { name: "Clear communication", icon: "fa-solid fa-comments", color: "blue" },
        { name: "Team collaboration", icon: "fa-solid fa-users", color: "purple" },
        { name: "Leadership mindset", icon: "fa-solid fa-chess-king", color: "yellow" },
        { name: "Time management", icon: "fa-solid fa-clock", color: "pink" },
        { name: "Continuous learning", icon: "fa-solid fa-infinity", color: "green" },
        { name: "Adaptability", icon: "fa-solid fa-shuffle", color: "blue" },
        { name: "Creativity", icon: "fa-solid fa-paint-brush", color: "purple" }
      ]
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      indigo: "text-indigo-400 border-indigo-400/60",
      blue: "text-blue-400 border-blue-400/60",
      purple: "text-purple-400 border-purple-400/60",
      green: "text-green-400 border-green-400/60",
      yellow: "text-yellow-400 border-yellow-400/60",
      pink: "text-pink-400 border-pink-400/60",
      gray: "text-gray-400 border-gray-400/60"
    };
    return colors[color] || "text-indigo-400 border-indigo-400/60";
  };

  const getSkillColor = (color) => {
    const colors = {
      indigo: "text-indigo-300",
      blue: "text-blue-300",
      purple: "text-purple-300",
      green: "text-green-300",
      yellow: "text-yellow-200",
      pink: "text-pink-300",
      gray: "text-gray-200"
    };
    return colors[color] || "text-indigo-300";
  };

  return (
    <section id="skills" className="py-16 md:py-24 bg-gradient-to-b from-bg-primary via-bg-secondary/60 to-bg-primary">
      <div className="container mx-auto">
        <h2 className="section-heading text-center mb-12 md:mb-16" data-animate>Core Skillset</h2>
        <div className="w-full max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-16">
          {skillCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="mb-10">
              <div className="flex items-center gap-3 mb-2">
                <i className={`${category.icon} w-7 h-7 ${getColorClasses(category.color).split(' ')[0]}`}></i>
                <span className="text-2xl font-bold text-white">{category.title}</span>
              </div>
              <div className={`flex flex-wrap gap-4 pl-10 border-l-4 ${getColorClasses(category.color).split(' ')[1]}`}>
                {category.skills.map((skill, skillIndex) => (
                  <span key={skillIndex} className={`font-semibold ${getSkillColor(skill.color)} flex items-center gap-2 text-lg`}>
                    <i className={skill.icon}></i> {skill.name}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Skills;
