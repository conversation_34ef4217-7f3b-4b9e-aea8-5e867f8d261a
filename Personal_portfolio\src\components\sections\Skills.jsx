import React, { useState } from 'react';

const Skills = () => {
  const [activeCategory, setActiveCategory] = useState(0);
  const [hoveredSkill, setHoveredSkill] = useState(null);

  const skillCategories = [
    {
      title: "Programming Languages",
      icon: "fas fa-code",
      color: "indigo",
      description: "Core programming languages I use for various development projects",
      skills: [
        { name: "C++", icon: "fa-brands fa-cuttlefish", color: "indigo", proficiency: 85, experience: "3+ years" },
        { name: "Java", icon: "fa-brands fa-java", color: "blue", proficiency: 80, experience: "2+ years" },
        { name: "Python", icon: "fa-brands fa-python", color: "yellow", proficiency: 90, experience: "4+ years" },
        { name: "C#", icon: "fa-solid fa-hashtag", color: "green", proficiency: 88, experience: "3+ years" },
        { name: "JavaScript", icon: "fa-brands fa-js", color: "yellow", proficiency: 85, experience: "3+ years" },
        { name: "TypeScript", icon: "fa-brands fa-js", color: "blue", proficiency: 75, experience: "2+ years" }
      ]
    },
    {
      title: "Web Development",
      icon: "fas fa-globe",
      color: "blue",
      description: "Modern web technologies for building responsive and interactive applications",
      skills: [
        { name: "HTML/CSS", icon: "fa-brands fa-html5", color: "pink", proficiency: 95, experience: "5+ years" },
        { name: "React", icon: "fa-brands fa-react", color: "blue", proficiency: 85, experience: "2+ years" },
        { name: "ASP.NET", icon: "fa-brands fa-microsoft", color: "blue", proficiency: 80, experience: "2+ years" },
        { name: "Tailwind CSS", icon: "fa-brands fa-css3-alt", color: "indigo", proficiency: 90, experience: "2+ years" },
        { name: "Node.js", icon: "fa-brands fa-node-js", color: "green", proficiency: 75, experience: "1+ years" },
        { name: "Next.js", icon: "fa-brands fa-react", color: "purple", proficiency: 70, experience: "1+ years" }
      ]
    },
    {
      title: "Mobile Development",
      icon: "fas fa-mobile-alt",
      color: "purple",
      description: "Cross-platform mobile app development with modern frameworks",
      skills: [
        { name: ".NET MAUI", icon: "fa-brands fa-microsoft", color: "purple", proficiency: 85, experience: "2+ years" },
        { name: "React Native", icon: "fa-brands fa-react", color: "blue", proficiency: 75, experience: "1+ years" },
        { name: "Android Studio", icon: "fa-brands fa-android", color: "green", proficiency: 70, experience: "2+ years" },
        { name: "Xamarin", icon: "fa-brands fa-microsoft", color: "purple", proficiency: 80, experience: "2+ years" },
        { name: "Flutter", icon: "fa-brands fa-google", color: "blue", proficiency: 65, experience: "1+ years" }
      ]
    },
    {
      title: "Databases & Backend",
      icon: "fas fa-database",
      color: "green",
      description: "Database design and backend services for scalable applications",
      skills: [
        { name: "Firebase", icon: "fa-brands fa-google", color: "green", proficiency: 85, experience: "2+ years" },
        { name: "MySQL", icon: "fa-solid fa-database", color: "yellow", proficiency: 80, experience: "3+ years" },
        { name: "PostgreSQL", icon: "fa-solid fa-database", color: "purple", proficiency: 75, experience: "2+ years" },
        { name: "MongoDB", icon: "fa-solid fa-database", color: "green", proficiency: 70, experience: "1+ years" },
        { name: "Supabase", icon: "fa-solid fa-database", color: "blue", proficiency: 80, experience: "1+ years" },
        { name: "SQL Server", icon: "fa-solid fa-database", color: "blue", proficiency: 85, experience: "3+ years" }
      ]
    },
    {
      title: "Tools, Software & Platforms",
      icon: "fas fa-tools",
      color: "pink",
      skills: [
        { name: "MATLAB", icon: "fa-solid fa-calculator", color: "pink" },
        { name: "Git/GitHub", icon: "fa-brands fa-git-alt", color: "gray" },
        { name: "Notion", icon: "fa-solid fa-note-sticky", color: "yellow" },
        { name: "Photoshop", icon: "fa-brands fa-adobe", color: "blue" },
        { name: "NetBeans", icon: "fa-brands fa-java", color: "green" },
        { name: "Xilinx Vivado", icon: "fa-solid fa-microchip", color: "purple" },
        { name: "LabVIEW", icon: "fa-solid fa-project-diagram", color: "pink" },
        { name: "Jupyter Notebooks", icon: "fa-solid fa-book", color: "yellow" },
        { name: "Office Suite", icon: "fa-solid fa-file-word", color: "blue" },
        { name: "MIT Scratch", icon: "fa-solid fa-brain", color: "pink" }
      ]
    },
    {
      title: "Hardware & IoT",
      icon: "fas fa-microchip",
      color: "yellow",
      skills: [
        { name: "Arduino UNO", icon: "fa-brands fa-usb", color: "yellow" },
        { name: "Arduino IDE", icon: "fa-solid fa-code", color: "blue" },
        { name: "ESP32/ESP8266", icon: "fa-solid fa-wifi", color: "green" },
        { name: "Raspberry Pi", icon: "fa-brands fa-raspberry-pi", color: "pink" },
        { name: "PictoBlox", icon: "fa-solid fa-cube", color: "yellow" }
      ]
    },
    {
      title: "Soft Skills",
      icon: "fas fa-users",
      color: "green",
      skills: [
        { name: "Problem-solving", icon: "fa-solid fa-lightbulb", color: "green" },
        { name: "Clear communication", icon: "fa-solid fa-comments", color: "blue" },
        { name: "Team collaboration", icon: "fa-solid fa-users", color: "purple" },
        { name: "Leadership mindset", icon: "fa-solid fa-chess-king", color: "yellow" },
        { name: "Time management", icon: "fa-solid fa-clock", color: "pink" },
        { name: "Continuous learning", icon: "fa-solid fa-infinity", color: "green" },
        { name: "Adaptability", icon: "fa-solid fa-shuffle", color: "blue" },
        { name: "Creativity", icon: "fa-solid fa-paint-brush", color: "purple" }
      ]
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      indigo: "text-indigo-400 border-indigo-400/60",
      blue: "text-blue-400 border-blue-400/60",
      purple: "text-purple-400 border-purple-400/60",
      green: "text-green-400 border-green-400/60",
      yellow: "text-yellow-400 border-yellow-400/60",
      pink: "text-pink-400 border-pink-400/60",
      gray: "text-gray-400 border-gray-400/60"
    };
    return colors[color] || "text-indigo-400 border-indigo-400/60";
  };

  const getSkillColor = (color) => {
    const colors = {
      indigo: "text-indigo-300",
      blue: "text-blue-300",
      purple: "text-purple-300",
      green: "text-green-300",
      yellow: "text-yellow-200",
      pink: "text-pink-300",
      gray: "text-gray-200"
    };
    return colors[color] || "text-indigo-300";
  };

  const getGradientClasses = (color) => {
    const gradients = {
      indigo: "from-indigo-500 to-blue-500",
      blue: "from-blue-500 to-cyan-500",
      purple: "from-purple-500 to-indigo-500",
      green: "from-green-500 to-emerald-500",
      yellow: "from-yellow-500 to-orange-500",
      pink: "from-pink-500 to-rose-500"
    };
    return gradients[color] || "from-indigo-500 to-blue-500";
  };

  const getSkillGradient = (color) => {
    const gradients = {
      indigo: "from-indigo-500 to-indigo-600",
      blue: "from-blue-500 to-blue-600",
      purple: "from-purple-500 to-purple-600",
      green: "from-green-500 to-green-600",
      yellow: "from-yellow-500 to-yellow-600",
      pink: "from-pink-500 to-pink-600",
      gray: "from-gray-500 to-gray-600"
    };
    return gradients[color] || "from-indigo-500 to-indigo-600";
  };

  return (
    <section id="skills" className="py-24 bg-gradient-to-b from-bg-primary via-bg-secondary/60 to-bg-primary relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-primary-color/10 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-tr from-secondary-color/8 to-transparent rounded-full blur-3xl animate-pulse-slow2"></div>
      </div>

      <div className="container mx-auto relative z-10">
        {/* Header */}
        <div className="text-center mb-20" data-animate>
          <h2 className="section-heading mb-6">Technical Expertise</h2>
          <div className="w-32 h-1 bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 mx-auto rounded-full mb-8"></div>
          <p className="text-slate-300 text-xl max-w-3xl mx-auto leading-relaxed font-light">
            A comprehensive overview of my technical skills and proficiency levels across various domains of software development.
          </p>
        </div>

        {/* Category Navigation */}
        <div className="flex flex-wrap justify-center gap-4 mb-16" data-animate>
          {skillCategories.map((category, index) => (
            <button
              key={index}
              onClick={() => setActiveCategory(index)}
              className={`flex items-center gap-3 px-6 py-3 rounded-full font-medium transition-all duration-300 border ${
                activeCategory === index
                  ? `bg-gradient-to-r ${getGradientClasses(category.color)} text-white border-transparent shadow-lg scale-105`
                  : `border-slate-600/50 text-slate-400 hover:text-white hover:border-slate-500/70 hover:bg-slate-800/50`
              }`}
            >
              <i className={`${category.icon} text-lg`}></i>
              <span className="hidden sm:inline">{category.title}</span>
            </button>
          ))}
        </div>

        {/* Active Category Display */}
        <div className="max-w-6xl mx-auto" data-animate>
          <div className="bg-gradient-to-br from-slate-800/60 to-slate-900/60 rounded-3xl p-8 md:p-12 border border-indigo-500/20 backdrop-blur-sm">
            <div className="text-center mb-12">
              <div className={`inline-flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br ${getGradientClasses(skillCategories[activeCategory].color)} shadow-lg mb-6`}>
                <i className={`${skillCategories[activeCategory].icon} text-3xl text-white`}></i>
              </div>
              <h3 className="text-3xl font-bold text-white mb-4">{skillCategories[activeCategory].title}</h3>
              <p className="text-slate-300 text-lg max-w-2xl mx-auto">{skillCategories[activeCategory].description}</p>
            </div>

            {/* Skills Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {skillCategories[activeCategory].skills.map((skill, skillIndex) => (
                <div
                  key={skillIndex}
                  className="group relative bg-slate-700/30 rounded-2xl p-6 border border-slate-600/30 hover:border-slate-500/50 transition-all duration-300 hover:-translate-y-1 cursor-pointer"
                  onMouseEnter={() => setHoveredSkill(`${activeCategory}-${skillIndex}`)}
                  onMouseLeave={() => setHoveredSkill(null)}
                >
                  {/* Skill Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${getSkillGradient(skill.color)} flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300`}>
                        <i className={`${skill.icon} text-lg text-white`}></i>
                      </div>
                      <div>
                        <h4 className="font-bold text-white group-hover:text-indigo-300 transition-colors">{skill.name}</h4>
                        <p className="text-slate-400 text-sm">{skill.experience}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-indigo-400">{skill.proficiency}%</div>
                    </div>
                  </div>

                  {/* Proficiency Bar */}
                  <div className="relative">
                    <div className="w-full bg-slate-600/50 rounded-full h-2 mb-2">
                      <div
                        className={`h-2 rounded-full bg-gradient-to-r ${getSkillGradient(skill.color)} transition-all duration-1000 ease-out`}
                        style={{
                          width: hoveredSkill === `${activeCategory}-${skillIndex}` ? `${skill.proficiency}%` : '0%'
                        }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-slate-500">
                      <span>Beginner</span>
                      <span>Expert</span>
                    </div>
                  </div>

                  {/* Hover Effect */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${getSkillGradient(skill.color)} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`}></div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Skills Summary */}
        <div className="mt-20 text-center" data-animate>
          <div className="bg-gradient-to-r from-slate-800/60 via-slate-800/40 to-slate-800/60 rounded-3xl p-12 border border-indigo-500/20 backdrop-blur-lg max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-6">Continuous Learning Journey</h3>
            <p className="text-slate-300 text-lg mb-8 leading-relaxed">
              Technology evolves rapidly, and so do I. I'm constantly learning new technologies,
              improving existing skills, and staying updated with industry best practices.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-indigo-400 mb-2">5+</div>
                <div className="text-slate-400 text-sm">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-400 mb-2">20+</div>
                <div className="text-slate-400 text-sm">Technologies</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-400 mb-2">50+</div>
                <div className="text-slate-400 text-sm">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-400 mb-2">∞</div>
                <div className="text-slate-400 text-sm">Learning Mindset</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
