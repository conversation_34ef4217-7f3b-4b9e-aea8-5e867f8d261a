// Project filtering functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const projectSections = document.querySelectorAll('.project-section');
    let isAnimating = false;

    // Handle tab switching with smooth animations
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            if (isAnimating) return; // Prevent animation interruption
            
            const category = button.getAttribute('data-category');
            const activeSection = document.getElementById(category);
            
            if (activeSection.classList.contains('active')) return; // Skip if already active
            
            isAnimating = true;

            // Smooth button transition
            tabButtons.forEach(btn => {
                btn.classList.remove('active-tab');
                btn.style.transform = 'scale(1)';
                btn.style.opacity = '0.7';
            });

            // Animate active button
            button.classList.add('active-tab');
            button.style.transform = 'scale(1.05)';
            button.style.opacity = '1';

            // Stagger card animations on exit
            const currentSection = document.querySelector('.project-section:not(.hidden)');
            const currentCards = currentSection?.querySelectorAll('.card') || [];
            
            currentCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'scale(0.95) translateY(10px)';
                }, index * 50);
            });

            // Section transition
            setTimeout(() => {
                projectSections.forEach(section => {
                    section.classList.add('hidden');
                    section.style.opacity = '0';
                });

                // Show new section
                activeSection.classList.remove('hidden');
                activeSection.style.opacity = '0';
                
                // Stagger card animations on enter
                const newCards = activeSection.querySelectorAll('.card');
                newCards.forEach((card, index) => {
                    card.style.opacity = '0';
                    card.style.transform = 'scale(0.95) translateY(20px)';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'scale(1) translateY(0)';
                    }, index * 100 + 50);
                });

                activeSection.style.opacity = '1';
                
                setTimeout(() => {
                    isAnimating = false;
                }, newCards.length * 100 + 100);
            }, 300);
        });
    });

    // Initialize AOS
    AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true
    });

    // Enhanced hover effects for project cards
    const projectCards = document.querySelectorAll('.card');
    projectCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            if (!isAnimating) {
                card.style.transform = 'translateY(-8px) scale(1.01)';
                card.style.transition = 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)';
                
                // Animate card contents
                const thumb = card.querySelector('.project-thumb i');
                const title = card.querySelector('h3');
                const tags = card.querySelectorAll('.flex-wrap span');
                
                if (thumb) thumb.style.transform = 'scale(1.1) translateY(-2px)';
                if (title) title.style.transform = 'translateY(-2px)';
                tags.forEach((tag, i) => {
                    tag.style.transform = 'translateY(-2px)';
                    tag.style.transition = `all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) ${i * 0.05}s`;
                });
            }
        });
        
        card.addEventListener('mouseleave', () => {
            if (!isAnimating) {
                card.style.transform = 'translateY(0) scale(1)';
                card.style.transition = 'all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)';
                
                // Reset card contents
                const thumb = card.querySelector('.project-thumb i');
                const title = card.querySelector('h3');
                const tags = card.querySelectorAll('.flex-wrap span');
                
                if (thumb) thumb.style.transform = 'scale(1) translateY(0)';
                if (title) title.style.transform = 'translateY(0)';
                tags.forEach(tag => {
                    tag.style.transform = 'translateY(0)';
                });
            }
        });
    });
});
