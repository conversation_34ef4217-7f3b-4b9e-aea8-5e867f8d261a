import React from 'react';

const LoadingSpinner = ({ size = 'md', color = 'indigo' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const colorClasses = {
    indigo: 'border-indigo-500',
    blue: 'border-blue-500',
    purple: 'border-purple-500',
    green: 'border-green-500'
  };

  return (
    <div className={`${sizeClasses[size]} border-2 ${colorClasses[color]} border-t-transparent rounded-full animate-spin`}></div>
  );
};

export const PageLoader = () => {
  return (
    <div className="fixed inset-0 bg-bg-primary flex items-center justify-center z-50">
      <div className="text-center">
        <div className="relative mb-8">
          <div className="w-20 h-20 border-4 border-indigo-500/30 border-t-indigo-500 rounded-full animate-spin"></div>
          <div className="absolute inset-0 w-20 h-20 border-4 border-transparent border-r-blue-500 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
        </div>
        <h2 className="text-2xl font-bold text-white mb-2">Loading Portfolio</h2>
        <p className="text-slate-400">Preparing an amazing experience...</p>
        <div className="mt-6 flex justify-center space-x-1">
          <div className="w-2 h-2 bg-indigo-500 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    </div>
  );
};

export const SkeletonCard = () => {
  return (
    <div className="bg-slate-800/50 rounded-3xl p-8 border border-slate-700/50 animate-pulse">
      <div className="skeleton h-48 rounded-2xl mb-6"></div>
      <div className="skeleton h-6 rounded mb-4"></div>
      <div className="skeleton h-4 rounded mb-2"></div>
      <div className="skeleton h-4 rounded w-3/4 mb-6"></div>
      <div className="flex gap-2 mb-6">
        <div className="skeleton h-6 w-16 rounded-full"></div>
        <div className="skeleton h-6 w-20 rounded-full"></div>
        <div className="skeleton h-6 w-14 rounded-full"></div>
      </div>
      <div className="flex gap-3">
        <div className="skeleton h-10 flex-1 rounded-xl"></div>
        <div className="skeleton h-10 w-10 rounded-xl"></div>
      </div>
    </div>
  );
};

export default LoadingSpinner;
