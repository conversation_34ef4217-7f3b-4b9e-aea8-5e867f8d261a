/* Custom styles moved from index.html */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

 /* Unified Premium Color Theme */
        :root {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --primary-color: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --secondary-color: #06b6d4;
            --secondary-light: #67e8f9;
            --accent-color: #8b5cf6;
            --accent-light: #a78bfa;
            --text-primary: #f8fafc;
            --text-secondary: #e2e8f0;
            --text-muted: #94a3b8;
            --border-color: rgba(99, 102, 241, 0.2);
            --card-bg: rgba(30, 41, 59, 0.8);
            --glass-bg: rgba(15, 23, 42, 0.9);
            --shadow-primary: rgba(99, 102, 241, 0.25);
            --shadow-secondary: rgba(6, 182, 212, 0.15);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
            color: var(--text-primary);
            overflow-x: hidden;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Optimized Background Animation */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
            will-change: transform;
        }

        .bg-animation::before,
        .bg-animation::after {
            content: '';
            position: absolute;
            border-radius: 50%;
            opacity: 0.15;
            will-change: transform;
        }

        .bg-animation::before {
            width: min(400px, 50vw);
            height: min(400px, 50vw);
            background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
            animation: move-glow-1 20s infinite alternate ease-in-out;
            top: 10%;
            left: 10%;
        }

        .bg-animation::after {
            width: min(300px, 40vw);
            height: min(300px, 40vw);
            background: radial-gradient(circle, var(--secondary-color) 0%, transparent 70%);
            animation: move-glow-2 25s infinite alternate-reverse ease-in-out;
            bottom: 10%;
            right: 10%;
        }

        @keyframes move-glow-1 {
            0% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(30vw, 20vh) scale(1.2); }
            100% { transform: translate(60vw, 40vh) scale(0.8); }
        }

        @keyframes move-glow-2 {
            0% { transform: translate(0, 0) scale(0.8); }
            50% { transform: translate(-20vw, -30vh) scale(1.1); }
            100% { transform: translate(-40vw, -10vh) scale(1); }
        }

        /* Ultra-Premium Minimalist Navbar */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            backdrop-filter: blur(24px) saturate(180%);
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.8) 0%,
                rgba(30, 41, 59, 0.75) 50%,
                rgba(15, 23, 42, 0.8) 100%);
            border-bottom: 1px solid rgba(99, 102, 241, 0.1);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            box-shadow: 0 1px 40px rgba(0, 0, 0, 0.1);
        }

        .navbar.scrolled {
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.95) 0%,
                rgba(30, 41, 59, 0.9) 50%,
                rgba(15, 23, 42, 0.95) 100%);
            border-bottom: 1px solid rgba(99, 102, 241, 0.2);
            box-shadow:
                0 8px 32px rgba(99, 102, 241, 0.15),
                0 1px 40px rgba(0, 0, 0, 0.2);
        }

        .navbar-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 80px;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 600;
            font-size: 1.25rem;
            letter-spacing: -0.025em;
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .navbar-brand::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .navbar-brand:hover::after {
            width: 100%;
        }

        .navbar-brand:hover {
            transform: translateY(-1px);
        }

        .navbar-logo {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border: 2px solid rgba(99, 102, 241, 0.3);
            transition: all 0.3s ease;
            filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.2));
        }

        .navbar-brand:hover .navbar-logo {
            border-color: var(--primary-color);
            transform: rotate(5deg) scale(1.05);
            filter: drop-shadow(0 6px 12px rgba(99, 102, 241, 0.3));
        }

        .navbar-nav {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .navbar-item {
            position: relative;
            padding: 0.625rem 1.25rem;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 0.9rem;
            letter-spacing: 0.025em;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .navbar-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(99, 102, 241, 0.1),
                transparent);
            transition: left 0.5s ease;
        }

        .navbar-item::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .navbar-item:hover {
            color: var(--text-primary);
            background: rgba(99, 102, 241, 0.08);
            transform: translateY(-1px);
        }

        .navbar-item:hover::before {
            left: 100%;
        }

        .navbar-item:hover::after {
            width: 80%;
        }

        .navbar-item.active {
            color: var(--primary-color);
            background: rgba(99, 102, 241, 0.12);
            font-weight: 600;
        }

        .navbar-item.active::after {
            width: 80%;
        }

        .navbar-cta {
            margin-left: 1rem;
            padding: 0.625rem 1.5rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.875rem;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
            position: relative;
            overflow: hidden;
        }

        .navbar-cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            transition: left 0.6s ease;
        }

        .navbar-cta:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        }

        .navbar-cta:hover::before {
            left: 100%;
        }

        /* Ultra-Premium Mobile Menu */
        .mobile-menu-toggle {
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 44px;
            height: 44px;
            border-radius: 12px;
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.2);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
        }

        .mobile-menu-toggle:hover {
            background: rgba(99, 102, 241, 0.15);
            border-color: rgba(99, 102, 241, 0.3);
            transform: scale(1.05);
        }

        .hamburger {
            position: relative;
            width: 20px;
            height: 14px;
        }

        .hamburger span {
            position: absolute;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 1px;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            transform-origin: center;
        }

        .hamburger span:nth-child(1) {
            top: 0;
        }

        .hamburger span:nth-child(2) {
            top: 6px;
        }

        .hamburger span:nth-child(3) {
            top: 12px;
        }

        .hamburger.active span:nth-child(1) {
            top: 6px;
            transform: rotate(45deg);
        }

        .hamburger.active span:nth-child(2) {
            opacity: 0;
            transform: scale(0);
        }

        .hamburger.active span:nth-child(3) {
            top: 6px;
            transform: rotate(-45deg);
        }

        .mobile-menu {
            position: fixed;
            top: 0;
            right: -100%;
            width: 100%;
            max-width: 380px;
            height: 100vh;
            background: linear-gradient(135deg,
                rgba(15, 23, 42, 0.95) 0%,
                rgba(30, 41, 59, 0.9) 50%,
                rgba(15, 23, 42, 0.95) 100%);
            backdrop-filter: blur(24px) saturate(180%);
            border-left: 1px solid rgba(99, 102, 241, 0.2);
            transition: right 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: 1001;
            overflow-y: auto;
            box-shadow: -10px 0 40px rgba(0, 0, 0, 0.3);
        }

        .mobile-menu.open {
            right: 0;
        }

        .mobile-menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(4px);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
        }

        .mobile-menu-overlay.open {
            opacity: 1;
            visibility: visible;
        }

        .mobile-menu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2rem 2rem 1rem;
            border-bottom: 1px solid rgba(99, 102, 241, 0.1);
        }

        .mobile-menu-close {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.2);
            color: var(--text-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mobile-menu-close:hover {
            background: rgba(99, 102, 241, 0.2);
            transform: scale(1.05);
        }

        .mobile-menu-nav {
            padding: 2rem;
        }

        .mobile-menu-item {
            display: block;
            padding: 1rem 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 1.1rem;
            text-decoration: none;
            border-radius: 16px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .mobile-menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(99, 102, 241, 0.1),
                transparent);
            transition: left 0.5s ease;
        }

        .mobile-menu-item:hover {
            color: var(--text-primary);
            background: rgba(99, 102, 241, 0.1);
            transform: translateX(8px);
        }

        .mobile-menu-item:hover::before {
            left: 100%;
        }

        .mobile-menu-item i {
            width: 24px;
            margin-right: 1rem;
            color: var(--primary-color);
        }

        .mobile-menu-cta {
            margin: 2rem;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--text-primary);
            font-weight: 600;
            text-align: center;
            border-radius: 16px;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
            position: relative;
            overflow: hidden;
        }

        .mobile-menu-cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            transition: left 0.6s ease;
        }

        .mobile-menu-cta:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4);
        }

        .mobile-menu-cta:hover::before {
            left: 100%;
        }

        /* Premium Section Headings */
        .section-heading {
            position: relative;
            display: inline-block;
            font-weight: 700;
            font-size: clamp(2rem, 5vw, 3rem);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .section-heading::after {
            content: '';
            position: absolute;
            width: 60px;
            height: 4px;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }

        /* Premium Card Styles */
        .card {
            background: var(--card-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-color);
            border-radius: 1.5rem;
            padding: 2rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 25px 50px var(--shadow-primary),
                0 0 0 1px rgba(99, 102, 241, 0.1);
            border-color: var(--primary-color);
        }

        .card:hover::before {
            opacity: 0.05;
        }

        /* Glass Effect Cards */
        .glass-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1.5rem;
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            background: rgba(30, 41, 59, 0.6);
            border-color: var(--primary-color);
            transform: translateY(-4px);
        }

        /* Responsive Utilities */
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        @media (min-width: 640px) {
            .container { padding: 0 2rem; }
        }

        @media (min-width: 1024px) {
            .container { padding: 0 3rem; }
        }

        /* Responsive Grid System */
        .grid {
            display: grid;
            gap: 1.5rem;
        }

        .grid-cols-1 { grid-template-columns: repeat(1, 1fr); }

        @media (min-width: 640px) {
            .sm\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
        }

        @media (min-width: 768px) {
            .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
            .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
        }

        @media (min-width: 1024px) {
            .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
            .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
        }

        /* Responsive Text */
        .text-responsive {
            font-size: clamp(1rem, 2.5vw, 1.25rem);
        }

        .heading-responsive {
            font-size: clamp(1.5rem, 4vw, 2.5rem);
        }

        .title-responsive {
            font-size: clamp(2rem, 6vw, 4rem);
        }

        /* Premium Button System */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            text-decoration: none;
            border-radius: 0.75rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            line-height: 1.5;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: var(--text-primary);
            box-shadow: 0 4px 20px var(--shadow-primary);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-light), var(--accent-color));
            box-shadow: 0 8px 30px var(--shadow-primary);
            transform: translateY(-2px) scale(1.02);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary-color), var(--secondary-light));
            color: var(--text-primary);
            box-shadow: 0 4px 20px var(--shadow-secondary);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, var(--secondary-light), var(--secondary-color));
            box-shadow: 0 8px 30px var(--shadow-secondary);
            transform: translateY(-2px) scale(1.02);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        .btn-glass {
            background: rgba(99, 102, 241, 0.1);
            backdrop-filter: blur(10px);
            color: var(--primary-color);
            border: 1px solid rgba(99, 102, 241, 0.3);
        }

        .btn-glass:hover {
            background: rgba(99, 102, 241, 0.2);
            border-color: var(--primary-color);
            color: var(--text-primary);
        }

        .btn-lg {
            padding: 1rem 2rem;
            font-size: 1.125rem;
            border-radius: 1rem;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            border-radius: 0.5rem;
        }

        /* Button Glow Effect */
        .btn-glow::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: inherit;
            border-radius: inherit;
            filter: blur(8px);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .btn-glow:hover::before {
            opacity: 0.7;
        }

        /* Legacy button support */
        .premium-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            font-weight: 600;
            text-decoration: none;
            border-radius: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            border: none;
            cursor: pointer;
            font-size: 1.125rem;
            line-height: 1.5;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: var(--text-primary);
            box-shadow: 0 4px 20px var(--shadow-primary);
        }

        .premium-btn:hover {
            background: linear-gradient(135deg, var(--primary-light), var(--accent-color));
            box-shadow: 0 8px 30px var(--shadow-primary);
            transform: translateY(-2px) scale(1.02);
        }

        /* Animation Keyframes */
        @keyframes wave {
            0% { transform: rotate(0deg); }
            10% { transform: rotate(14deg); }
            20% { transform: rotate(-8deg); }
            30% { transform: rotate(14deg); }
            40% { transform: rotate(-4deg); }
            50% { transform: rotate(10deg); }
            60% { transform: rotate(0deg); }
            100% { transform: rotate(0deg); }
        }

        .animate-wave {
            animation: wave 1.2s cubic-bezier(.36,.07,.19,.97) both;
            transform-origin: 70% 70%;
        }

        @keyframes pulse-slow {
            0%, 100% { opacity: 0.4; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.08); }
        }

        .animate-pulse-slow {
            animation: pulse-slow 6s ease-in-out infinite;
        }

        @keyframes pulse-slow2 {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 0.6; transform: scale(1.12); }
        }

        .animate-pulse-slow2 {
            animation: pulse-slow2 8s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-30px); }
        }

        .animate-float {
            animation: float 7s ease-in-out infinite;
        }

        /* Touch-friendly interactions */
        .touch-target {
            min-height: 44px;
            min-width: 44px;
        }

        .btn, .navbar-item, .glass-card {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
        }

        /* Improved mobile animations */
        @media (prefers-reduced-motion: no-preference) {
            .card, .glass-card, .btn {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
        }

        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Navbar Responsive Breakpoints */
        @media (max-width: 1024px) {
            .navbar-nav {
                display: none;
            }

            .mobile-menu-toggle {
                display: flex;
            }

            .navbar-container {
                padding: 0 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .navbar-container {
                height: 70px;
                padding: 0 1rem;
            }

            .navbar-brand {
                font-size: 1.125rem;
            }

            .navbar-logo {
                width: 32px;
                height: 32px;
            }

            .mobile-menu {
                max-width: 100%;
            }

            .container {
                padding: 0 1rem;
            }

            .section-heading {
                font-size: clamp(1.75rem, 4vw, 2.5rem);
            }

            .card {
                padding: 1.5rem;
                margin-bottom: 1rem;
            }

            .glass-card {
                padding: 0.75rem 1rem;
            }
        }

        @media (max-width: 640px) {
            .title-responsive {
                font-size: clamp(1.75rem, 6vw, 2.5rem);
                line-height: 1.2;
            }

            .heading-responsive {
                font-size: clamp(1.25rem, 4vw, 1.75rem);
            }

            .btn-lg {
                padding: 0.875rem 1.75rem;
                font-size: 1rem;
                width: 100%;
                max-width: 280px;
            }

            .mobile-menu {
                max-width: 100%;
                padding: 1.5rem;
            }

            .navbar-brand {
                font-size: 1.25rem;
            }

            /* Stack buttons vertically on mobile */
            .flex.flex-col.sm\\:flex-row {
                flex-direction: column;
                gap: 1rem;
            }
        }

        @media (max-width: 480px) {
            .navbar-container {
                height: 60px;
                padding: 0 0.75rem;
            }

            .navbar-brand {
                font-size: 1rem;
            }

            .navbar-logo {
                width: 28px;
                height: 28px;
            }

            .mobile-menu-toggle {
                width: 40px;
                height: 40px;
            }

            .hamburger {
                width: 18px;
                height: 12px;
            }

            .title-responsive {
                font-size: clamp(1.5rem, 8vw, 2rem);
            }

            .glass-card, .inline-flex.items-center.gap-2 {
                padding: 0.5rem 0.75rem;
                font-size: 0.75rem;
            }

            .container {
                padding: 0 0.75rem;
            }

            .card {
                padding: 1rem;
            }

            main {
                padding-top: 60px !important;
            }
        }

        @media (max-width: 360px) {
            .navbar-brand span {
                display: none;
            }

            .title-responsive {
                font-size: 1.5rem;
            }

            .heading-responsive {
                font-size: 1.125rem;
            }

            .btn-lg {
                padding: 0.75rem 1.25rem;
                font-size: 0.875rem;
            }
        }

        /* Landscape mobile optimization */
        @media (max-height: 500px) and (orientation: landscape) {
            #home {
                min-height: 100vh;
                padding: 2rem 0;
            }

            .title-responsive {
                font-size: 1.5rem;
            }

            .heading-responsive {
                font-size: 1.125rem;
            }
        }

        /* Loading states and transitions */
        body:not(.loaded) {
            overflow: hidden;
        }

        [data-animate] {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1),
                        transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body.loaded [data-animate] {
            opacity: 1;
            transform: translateY(0);
        }

        /* Improved focus states for accessibility */
        .btn:focus-visible,
        .navbar-item:focus-visible {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            :root {
                --text-primary: #ffffff;
                --text-secondary: #ffffff;
                --border-color: rgba(255, 255, 255, 0.5);
            }
        }

        /* Dark mode enhancements */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-primary: #000000;
                --bg-secondary: #111111;
                --text-primary: #ffffff;
                --text-secondary: #e5e5e5;
            }
        }

        /* Print styles */
        @media print {
            .navbar,
            .mobile-menu,
            .mobile-menu-overlay,
            .bg-animation {
                display: none !important;
            }

            body {
                background: white !important;
                color: black !important;
            }

            .card {
                border: 1px solid #ccc !important;
                background: white !important;
            }
        }

        /* Smooth scrolling for all browsers */
        html {
            scroll-behavior: smooth;
        }

        @media (prefers-reduced-motion: reduce) {
            html {
                scroll-behavior: auto;
            }
        }
        

:root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --primary-color: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --secondary-color: #06b6d4;
    --secondary-light: #67e8f9;
    --accent-color: #8b5cf6;
    --accent-light: #a78bfa;
    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #94a3b8;
    --border-color: rgba(99, 102, 241, 0.2);
    --card-bg: rgba(30, 41, 59, 0.8);
    --glass-bg: rgba(15, 23, 42, 0.9);
    --shadow-primary: rgba(99, 102, 241, 0.25);
    --shadow-secondary: rgba(6, 182, 212, 0.15);
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-primary) 100%);
    color: var(--text-primary);
    overflow-x: hidden;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.bg-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.bg-animation::before {
    content: '';
    position: absolute;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
    border-radius: 50%;
    animation: move-glow 15s infinite alternate;
    opacity: 0.3;
}

@keyframes move-glow {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
        transform: translate(150vw, 150vh) rotate(360deg);
    }
}

.navbar-item {
    position: relative;
    transition: all 0.3s ease;
    color: var(--text-color);
}

.navbar-item::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background-color: var(--primary-color);
    transition: all 0.3s ease;
}

.navbar-item:hover {
    color: var(--primary-color);
}

.navbar-item:hover::after {
    width: 100%;
    left: 0;
}

.section-heading {
    position: relative;
    display: inline-block;
    font-weight: 700;
    color: var(--secondary-color);
}

.section-heading::after {
    content: '';
    position: absolute;
    width: 70%;
    height: 3px;
    bottom: -10px;
    left: 15%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.card {
    background: linear-gradient(120deg, rgba(24,28,43,0.95) 60%, rgba(44,182,125,0.10) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid var(--primary-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 1rem;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(129, 140, 248, 0.2);
}

.skill-bar {
    height: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    width: 0;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.shadow-neumorphic {
    box-shadow: 8px 8px 16px #0c1221, -8px -8px 16px #121c33;
}

/* Premium Button Styles */
.premium-btn, .send-message-btn, .download-cv-btn, .navbar-item, .inline-block.bg-primary-color {
    position: relative;
    display: inline-block;
    font-weight: 600;
    border-radius: 9999px;
    background: linear-gradient(90deg, #3b82f6 0%, #818cf8 50%, #c7d2fe 100%);
    color: #fff;
    box-shadow: 0 4px 24px 0 rgba(129,140,248,0.25), 0 1.5px 6px 0 rgba(59,130,246,0.15);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s, background 0.3s;
}
.premium-btn:hover, .send-message-btn:hover, .download-cv-btn:hover, .navbar-item:hover, .inline-block.bg-primary-color:hover {
    background: linear-gradient(90deg, #818cf8 0%, #3b82f6 50%, #c7d2fe 100%);
    box-shadow: 0 8px 32px 0 rgba(129,140,248,0.35), 0 3px 12px 0 rgba(59,130,246,0.25);
    transform: scale(1.05);
}
.premium-btn:active, .send-message-btn:active, .download-cv-btn:active, .navbar-item:active, .inline-block.bg-primary-color:active {
    transform: scale(0.98);
}
.premium-btn .btn-glow, .send-message-btn .btn-glow, .download-cv-btn .btn-glow {
    position: absolute;
    inset: 0;
    border-radius: inherit;
    pointer-events: none;
    box-shadow: 0 0 24px 8px #818cf8, 0 0 48px 16px #3b82f6;
    opacity: 0.5;
    animation: btn-glow-anim 2s linear infinite;
}
@keyframes btn-glow-anim {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

@keyframes wave {
  0% { transform: rotate(0deg); }
  10% { transform: rotate(14deg); }
  20% { transform: rotate(-8deg); }
  30% { transform: rotate(14deg); }
  40% { transform: rotate(-4deg); }
  50% { transform: rotate(10deg); }
  60% { transform: rotate(0deg); }
  100% { transform: rotate(0deg); }
}
.animate-wave {
  animation: wave 1.2s cubic-bezier(.36,.07,.19,.97) both;
  transform-origin: 70% 70%;
}
@keyframes pulse-slow {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.08); }
}
.animate-pulse-slow {
  animation: pulse-slow 6s ease-in-out infinite;
}
@keyframes pulse-slow2 {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.12); }
}
.animate-pulse-slow2 {
  animation: pulse-slow2 8s ease-in-out infinite;
}
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-30px); }
}
.animate-float {
  animation: float 7s ease-in-out infinite;
}