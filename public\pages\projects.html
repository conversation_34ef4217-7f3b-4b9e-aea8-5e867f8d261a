<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<PERSON>'s Projects - Explore my diverse portfolio of software development, hardware engineering, and creative experiments.">
    <meta name="keywords" content="<PERSON>, Projects, Portfolio, Software Development, Hardware Engineering, Web Development, Mobile Apps">
    <meta name="author" content="<PERSON>">
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="Projects | Abdul Basit Memon">
    <meta property="og:description" content="Explore my diverse portfolio of projects spanning software development, hardware engineering, and creative experiments.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://engrabm.com/projects">
    <title>Projects | Abdul Basit Memon</title>

    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"></noscript>
    <link rel="preload" href="../images/abm.png" as="image">

    <!-- Critical CSS -->
    <link rel="stylesheet" href="../src/styles.css">
    <link rel="stylesheet" href="../src/footer-premium.css">

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//cdn.tailwindcss.com">

    <!-- External CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Defer non-critical JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
   
</head>
<body>
    <!-- Simple Minimalist Navbar -->
    <nav class="navbar fixed w-full top-0 z-50 bg-[#171723]/90 backdrop-blur-sm border-b border-white/5"
         x-data="{ isOpen: false }"
         @keydown.escape="isOpen = false">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Brand -->
                <a href="../index.html#home" class="flex items-center">
                    <span class="text-white font-medium text-lg tracking-wide">ABM</span>
                </a>
            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="../index.html#home" class="nav-link">Home</a>
                <a href="../index.html#about" class="nav-link">About</a>
                <a href="./projects.html" class="nav-link">Projects</a>
                <a href="../index.html#services" class="nav-link">Services</a>
                <a href="../index.html#contact" class="nav-link">Contact</a>
                <a href="https://abdulbasitmemon.hashnode.dev/"
                   class="nav-link"
                   target="_blank"
                   rel="noopener">Blog</a>
                <a href="https://nas.io/prologware-solutions-3"
                   class="inline-flex items-center px-4 py-2 rounded-lg bg-indigo-600 text-white text-sm font-medium hover:bg-indigo-700 transition-colors"
                   target="_blank"
                   rel="noopener">
                    <i class="fas fa-users mr-2"></i>
                    Join Community
                </a>
            </div>

            <!-- Mobile Menu Button -->
            <button class="md:hidden text-gray-300 hover:text-white"
                    @click="isOpen = !isOpen"
                    :aria-expanded="isOpen"
                    aria-label="Toggle navigation menu">
                <svg class="h-6 w-6"
                     :class="{ 'hidden': isOpen }"
                     stroke="currentColor"
                     fill="none"
                     viewBox="0 0 24 24">
                    <path stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M4 6h16M4 12h16M4 18h16"/>
                </svg>
                <svg class="h-6 w-6"
                     :class="{ 'hidden': !isOpen }"
                     stroke="currentColor"
                     fill="none"
                     viewBox="0 0 24 24">
                    <path stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
            </div>
        </div>

        <!-- Simple Mobile Menu -->
        <div class="md:hidden"
             x-show="isOpen"
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 -translate-y-1"
             x-transition:enter-end="opacity-100 translate-y-0"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 translate-y-0"
             x-transition:leave-end="opacity-0 -translate-y-1"
             @click.away="isOpen = false"
             style="display: none;">

            <div class="bg-[#171723] border-t border-white/5">
                <div class="px-4 py-2 space-y-1">
                    <a href="../index.html#home" @click="isOpen = false"
                       class="mobile-link">
                        <i class="fas fa-home w-5"></i>
                        <span>Home</span>
                    </a>
                    <a href="../index.html#about" @click="isOpen = false"
                       class="mobile-link">
                        <i class="fas fa-user w-5"></i>
                        <span>About</span>
                    </a>
                    <a href="./projects.html" @click="isOpen = false"
                       class="mobile-link">
                        <i class="fas fa-code w-5"></i>
                        <span>Projects</span>
                    </a>
                    <a href="../index.html#services" @click="isOpen = false"
                       class="mobile-link">
                        <i class="fas fa-cogs w-5"></i>
                        <span>Services</span>
                    </a>
                    <a href="../index.html#contact" @click="isOpen = false"
                       class="mobile-link">
                        <i class="fas fa-envelope w-5"></i>
                        <span>Contact</span>
                    </a>
                    <a href="https://abdulbasitmemon.hashnode.dev/"
                       target="_blank"
                       rel="noopener"
                       class="mobile-link">
                        <i class="fas fa-blog w-5"></i>
                        <span>Blog</span>
                    </a>
                    <a href="https://nas.io/prologware-solutions-3"
                       target="_blank"
                       rel="noopener"
                       class="block px-4 py-2 mt-4 text-center text-white bg-indigo-600 rounded-lg hover:bg-indigo-700"
                       @click="isOpen = false">
                        <i class="fas fa-users mr-2"></i>
                        Join Community
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <style>
    .nav-link {
        color: rgb(209 213 219);
        font-size: 0.875rem;
        font-weight: 500;
        transition: color 0.2s;
        position: relative;
        padding: 0.5rem 0;
    }

    .nav-link:hover {
        color: white;
    }

    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background-color: rgb(99 102 241);
        transition: width 0.2s;
    }

    .nav-link:hover::after {
        width: 100%;
    }

    .mobile-link {
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
        color: rgb(209 213 219);
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s;
        border-radius: 0.5rem;
    }

    .mobile-link:hover {
        color: white;
        background-color: rgba(255, 255, 255, 0.05);
    }
    </style>
    <main style="padding-top: 80px;">
        <!-- Projects Hero Section - Following index.html theme -->
        <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
            <!-- Optimized background gradients -->
            <div class="absolute inset-0 bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary"></div>

            <!-- Animated background elements -->
            <div class="absolute inset-0 pointer-events-none">
                <div class="absolute top-1/4 left-1/4 w-64 h-64 md:w-96 md:h-96 bg-gradient-to-br from-primary-color/20 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
                <div class="absolute bottom-1/4 right-1/4 w-48 h-48 md:w-80 md:h-80 bg-gradient-to-tr from-secondary-color/15 to-transparent rounded-full blur-3xl animate-pulse-slow2"></div>
                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 md:w-48 md:h-48 bg-gradient-to-br from-accent-color/10 to-transparent rounded-full blur-2xl animate-float"></div>
            </div>

            <div class="container relative z-10 text-center py-20">
                <div class="max-w-4xl mx-auto space-y-8">
                    <!-- Premium Hero Typography - Following index.html style -->
                    <div class="space-y-6" data-animate>
                        <h1 class="title-responsive font-bold tracking-tight leading-tight">
                            <span class="block bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 bg-clip-text text-transparent font-extrabold drop-shadow-lg">
                                My Projects
                            </span>
                        </h1>
                        <div class="mx-auto w-24 h-1 bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 rounded-full mb-6"></div>
                        <p class="text-lg md:text-xl text-slate-300 font-light leading-relaxed">
                            A comprehensive showcase of my technical expertise across diverse domains.
                            Each project represents innovation, problem-solving, and commitment to excellence.
                        </p>
                    </div>
                </div>
            </div>
        </section>
        <!-- Projects Section - Following index.html theme exactly -->
        <section class="py-16 md:py-24 bg-gradient-to-b from-bg-primary via-bg-secondary/60 to-bg-primary">
            <div class="container mx-auto">
                <!-- Category Navigation -->
                <div class="flex flex-wrap justify-center gap-4 mb-16" data-animate>
                    <button class="category-filter active" data-category="all">
                        <i class="fas fa-th-large mr-2"></i>
                        All Projects
                    </button>
                    <button class="category-filter" data-category="software">
                        <i class="fas fa-laptop-code mr-2"></i>
                        Software
                    </button>
                    <button class="category-filter" data-category="hardware">
                        <i class="fas fa-microchip mr-2"></i>
                        Hardware
                    </button>
                    <button class="category-filter" data-category="tools">
                        <i class="fas fa-tools mr-2"></i>
                        Tools
                    </button>
                    <button class="category-filter" data-category="fun">
                        <i class="fas fa-gamepad mr-2"></i>
                        Fun Projects
                    </button>
                </div>
                <!-- Projects Grid - Following index.html structure exactly -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12 max-w-7xl mx-auto" id="projects-grid">

                    <!-- TechdioApp Project -->
                    <div class="group card relative overflow-hidden backdrop-blur-sm project-item" data-category="software" data-aos="fade-up">
                        <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-indigo-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                        <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-purple-400/20 via-indigo-400/10 to-blue-400/0 rounded-full blur-2xl opacity-30 z-0"></div>

                        <div class="project-thumb bg-gradient-to-br from-purple-500/80 to-indigo-400/80 flex items-center justify-center h-48 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg">
                            <i class="fas fa-graduation-cap fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110"></i>
                        </div>

                        <div class="p-8 relative z-10">
                            <div class="flex items-center gap-3 mb-3">
                                <span class="text-xs font-medium text-purple-300/80">2024</span>
                                <span class="w-1 h-1 bg-purple-300/50 rounded-full"></span>
                                <span class="text-xs font-medium text-purple-300/80">SOFTWARE</span>
                            </div>
                            <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">TechdioApp</h3>
                            <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A sleek, cross-platform learning hub on .NET MAUI. Browse curated tech courses, book one-on-one tutor sessions, and track your progress with offline-capable data syncing.</p>

                            <div class="flex flex-wrap gap-2 mb-6">
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">.NET MAUI</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">C#</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">SQLite</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">XAML</span>
                            </div>

                            <div class="flex items-center gap-3">
                                <a href="#" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                                    <span class="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-purple-500/30 group-hover/btn:to-indigo-500/30"></span>
                                    <span class="relative flex items-center justify-center">
                                        <i class="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                                        View Project
                                    </span>
                                </a>
                                <a href="https://github.com/abm1119/TechdioApp" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                                    <i class="fab fa-github fa-lg"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Carpooling App Project -->
                    <div class="group card relative overflow-hidden backdrop-blur-sm project-item" data-category="software" data-aos="fade-up" data-aos-delay="200">
                        <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-blue-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

                        <div class="project-thumb flex items-center justify-center h-48 rounded-t-3xl relative bg-gradient-to-br from-indigo-500/80 to-blue-400/80 shadow-2xl backdrop-blur-lg">
                            <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <i class="fas fa-car fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110"></i>
                        </div>

                        <div class="p-8 relative">
                            <div class="flex items-center gap-3 mb-3">
                                <span class="text-xs font-medium text-indigo-300/80">2024</span>
                                <span class="w-1 h-1 bg-indigo-300/50 rounded-full"></span>
                                <span class="text-xs font-medium text-indigo-300/80">MOBILE</span>
                            </div>
                            <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Carpooling App</h3>
                            <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A comprehensive carpooling solution built with React Native, featuring real-time ride matching, secure payments, and GPS tracking for seamless shared transportation.</p>

                            <div class="flex flex-wrap gap-2 mb-6">
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">React Native</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Firebase</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Maps API</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Payment Gateway</span>
                            </div>

                            <div class="flex items-center gap-3">
                                <a href="https://vimeo.com/1102623700" target="_blank" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                                    <span class="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-blue-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-indigo-500/30 group-hover/btn:to-blue-500/30"></span>
                                    <span class="relative flex items-center justify-center">
                                        <i class="fas fa-play mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                                        Watch Demo
                                    </span>
                                </a>
                                <a href="https://github.com/urooj-marvi/Carpooling_App" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                                    <i class="fab fa-github fa-lg"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Arduino Speed Detector Project -->
                    <div class="group card relative overflow-hidden backdrop-blur-sm project-item" data-category="hardware" data-aos="fade-up" data-aos-delay="400">
                        <div class="absolute inset-0 bg-gradient-to-br from-green-500/5 to-blue-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                        <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-green-400/20 via-blue-400/10 to-teal-400/0 rounded-full blur-2xl opacity-30 z-0"></div>

                        <div class="project-thumb bg-gradient-to-br from-green-500/80 to-blue-400/80 flex items-center justify-center h-48 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg">
                            <i class="fas fa-microchip fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110"></i>
                        </div>

                        <div class="p-8 relative z-10">
                            <div class="flex items-center gap-3 mb-3">
                                <span class="text-xs font-medium text-green-300/80">2024</span>
                                <span class="w-1 h-1 bg-green-300/50 rounded-full"></span>
                                <span class="text-xs font-medium text-green-300/80">HARDWARE</span>
                            </div>
                            <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Arduino Vehicle Speed Detector</h3>
                            <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">An IR-based Arduino project that calculates and displays vehicle speed using sensor break-beams and LCD output with high accuracy measurements.</p>

                            <div class="flex flex-wrap gap-2 mb-6">
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Arduino Uno</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">IR Sensors</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">C++</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">LCD Display</span>
                            </div>

                            <div class="flex items-center gap-3">
                                <a href="#" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                                    <span class="absolute inset-0 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-green-500/30 group-hover/btn:to-blue-500/30"></span>
                                    <span class="relative flex items-center justify-center">
                                        <i class="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                                        View Project
                                    </span>
                                </a>
                                <a href="https://github.com/abm1119/Arduino-Vehicle-Speed-Detector-" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                                    <i class="fab fa-github fa-lg"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <!-- Student Management System -->
                    <div class="group card relative overflow-hidden backdrop-blur-sm project-item" data-category="software" data-aos="fade-up">
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                        <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-blue-400/20 via-cyan-400/10 to-teal-400/0 rounded-full blur-2xl opacity-30 z-0"></div>

                        <div class="project-thumb bg-gradient-to-br from-blue-500/80 to-cyan-400/80 flex items-center justify-center h-48 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg">
                            <i class="fas fa-users fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110"></i>
                        </div>

                        <div class="p-8 relative z-10">
                            <div class="flex items-center gap-3 mb-3">
                                <span class="text-xs font-medium text-blue-300/80">2024</span>
                                <span class="w-1 h-1 bg-blue-300/50 rounded-full"></span>
                                <span class="text-xs font-medium text-blue-300/80">WEB</span>
                            </div>
                            <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Student Management System</h3>
                            <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A comprehensive web-based system for academic monitoring by class advisors. Built with ASP.NET and SQL Server integration for robust data management.</p>

                            <div class="flex flex-wrap gap-2 mb-6">
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">ASP.NET</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">C#</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">SQL Server</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Bootstrap</span>
                            </div>

                            <div class="flex items-center gap-3">
                                <a href="https://www.playbook.com/s/computer-systems-engineeing/2sP2yksKvEegmQSQi6tQJGRr?assetToken=ysdqT8vLdLbv2h1de2pYG6kg" target="_blank" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                                    <span class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-blue-500/30 group-hover/btn:to-cyan-500/30"></span>
                                    <span class="relative flex items-center justify-center">
                                         <i class="fas fa-play mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                                        Watch Demo
                                    </span>
                                </a>
                                <a href="https://github.com/abm1119/student-management-app" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                                    <i class="fab fa-github fa-lg"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Password Manager Vault -->
                    <div class="group card relative overflow-hidden backdrop-blur-sm project-item" data-category="tools" data-aos="fade-up" data-aos-delay="200">
                        <div class="absolute inset-0 bg-gradient-to-br from-yellow-500/5 to-orange-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                        <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-yellow-400/20 via-orange-400/10 to-red-400/0 rounded-full blur-2xl opacity-30 z-0"></div>

                        <div class="project-thumb bg-gradient-to-br from-yellow-500/80 to-orange-400/80 flex items-center justify-center h-48 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg">
                            <i class="fas fa-key fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110"></i>
                        </div>

                        <div class="p-8 relative z-10">
                            <div class="flex items-center gap-3 mb-3">
                                <span class="text-xs font-medium text-yellow-300/80">2024</span>
                                <span class="w-1 h-1 bg-yellow-300/50 rounded-full"></span>
                                <span class="text-xs font-medium text-yellow-300/80">DESKTOP</span>
                            </div>
                            <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Password Manager Vault</h3>
                            <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">Securely store and organize your credentials locally with encrypted storage, folder organization, and themed UI. No cloud, no tracking - complete privacy.</p>

                            <div class="flex flex-wrap gap-2 mb-6">
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Python</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Tkinter</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">SQLite</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Encryption</span>
                            </div>

                            <div class="flex items-center gap-3">
                                <a href="https://github.com/abm1119/password-manager-vault/blob/main/build/app/app.exe" target="_blank" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                                    <span class="absolute inset-0 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-yellow-500/30 group-hover/btn:to-orange-500/30"></span>
                                    <span class="relative flex items-center justify-center">
                                        <i class="fas fa-download mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                                        Download App
                                    </span>
                                </a>
                                <a href="https://github.com/abm1119/password-manager-vault" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                                    <i class="fab fa-github fa-lg"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- My Learning Track -->
                    <div class="group card relative overflow-hidden backdrop-blur-sm project-item" data-category="fun" data-aos="fade-up" data-aos-delay="400">
                        <div class="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-teal-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                        <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-cyan-400/20 via-teal-400/10 to-blue-400/0 rounded-full blur-2xl opacity-30 z-0"></div>

                        <div class="project-thumb bg-gradient-to-br from-cyan-500/80 to-teal-400/80 flex items-center justify-center h-48 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg">
                            <i class="fas fa-book-open fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110"></i>
                        </div>

                        <div class="p-8 relative z-10">
                            <div class="flex items-center gap-3 mb-3">
                                <span class="text-xs font-medium text-cyan-300/80">2024</span>
                                <span class="w-1 h-1 bg-cyan-300/50 rounded-full"></span>
                                <span class="text-xs font-medium text-cyan-300/80">LEARNING</span>
                            </div>
                            <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">My Learning Track</h3>
                            <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A personal learning hub with a custom HTML/CSS landing page, linked to structured Notion pages published using Super.so for organized knowledge sharing.</p>

                            <div class="flex flex-wrap gap-2 mb-6">
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">HTML</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">CSS</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Notion</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Super.so</span>
                            </div>

                            <div class="flex items-center gap-3">
                                <a href="https://abm1119.github.io/My-Learning-Track/" target="_blank" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                                    <span class="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-teal-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-cyan-500/30 group-hover/btn:to-teal-500/30"></span>
                                    <span class="relative flex items-center justify-center">
                                        <i class="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                                        Visit Site
                                    </span>
                                </a>
                                <a href="https://github.com/abm1119/My-Learning-Track" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                                    <i class="fab fa-github fa-lg"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    <!-- Image Background Removal Tool -->
                    <div class="group card relative overflow-hidden backdrop-blur-sm project-item" data-category="tools" data-aos="fade-up">
                        <div class="absolute inset-0 bg-gradient-to-br from-pink-500/5 to-purple-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                        <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-pink-400/20 via-purple-400/10 to-indigo-400/0 rounded-full blur-2xl opacity-30 z-0"></div>

                        <div class="project-thumb bg-gradient-to-br from-pink-500/80 to-purple-400/80 flex items-center justify-center h-48 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg">
                            <i class="fas fa-image fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110"></i>
                        </div>

                        <div class="p-8 relative z-10">
                            <div class="flex items-center gap-3 mb-3">
                                <span class="text-xs font-medium text-pink-300/80">2024</span>
                                <span class="w-1 h-1 bg-pink-300/50 rounded-full"></span>
                                <span class="text-xs font-medium text-pink-300/80">TOOL</span>
                            </div>
                            <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Image Background Removal</h3>
                            <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A Streamlit-based web application for removing image backgrounds using Python's rembg library. Simple, fast, and effective background removal tool.</p>

                            <div class="flex flex-wrap gap-2 mb-6">
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Python</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Streamlit</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">rembg</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">PIL</span>
                            </div>

                            <div class="flex items-center gap-3">
                                <a href="https://abm-bg-remover.streamlit.app/" target="_blank" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                                    <span class="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-pink-500/30 group-hover/btn:to-purple-500/30"></span>
                                    <span class="relative flex items-center justify-center">
                                        <i class="fas fa-external-link-alt mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                                        Try It Out
                                    </span>
                                </a>
                                <a href="https://github.com/abm1119/image_bg-Removal-app-" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                                    <i class="fab fa-github fa-lg"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Portfolio Website -->
                    <div class="group card relative overflow-hidden backdrop-blur-sm project-item" data-category="software" data-aos="fade-up" data-aos-delay="200">
                        <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                        <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-emerald-400/20 via-green-400/10 to-teal-400/0 rounded-full blur-2xl opacity-30 z-0"></div>

                        <div class="project-thumb bg-gradient-to-br from-emerald-500/80 to-green-400/80 flex items-center justify-center h-48 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg">
                            <i class="fas fa-globe fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110"></i>
                        </div>

                        <div class="p-8 relative z-10">
                            <div class="flex items-center gap-3 mb-3">
                                <span class="text-xs font-medium text-emerald-300/80">2024</span>
                                <span class="w-1 h-1 bg-emerald-300/50 rounded-full"></span>
                                <span class="text-xs font-medium text-emerald-300/80">WEB</span>
                            </div>
                            <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Portfolio Website</h3>
                            <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A modern, responsive portfolio website showcasing my projects and skills. Built with vanilla HTML, CSS, and JavaScript with premium animations and interactions.</p>

                            <div class="flex flex-wrap gap-2 mb-6">
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">HTML5</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">CSS3</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">JavaScript</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Responsive</span>
                            </div>

                            <div class="flex items-center gap-3">
                                <a href="../index.html" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                                    <span class="absolute inset-0 bg-gradient-to-r from-emerald-500/20 to-green-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-emerald-500/30 group-hover/btn:to-green-500/30"></span>
                                    <span class="relative flex items-center justify-center">
                                        <i class="fas fa-home mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                                        View Site
                                    </span>
                                </a>
                                <a href="https://github.com/abm1119/abdul-basit-portfolio" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                                    <i class="fab fa-github fa-lg"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Mini JavaScript Game -->
                    <div class="group card relative overflow-hidden backdrop-blur-sm project-item" data-category="fun" data-aos="fade-up" data-aos-delay="400">
                        <div class="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-500/5 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                        <div class="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-orange-400/20 via-red-400/10 to-pink-400/0 rounded-full blur-2xl opacity-30 z-0"></div>

                        <div class="project-thumb bg-gradient-to-br from-orange-500/80 to-red-400/80 flex items-center justify-center h-48 rounded-t-3xl mb-6 shadow-2xl backdrop-blur-lg">
                            <i class="fas fa-gamepad fa-4x text-white/90 drop-shadow-xl transform transition-all duration-500 group-hover:scale-110"></i>
                        </div>

                        <div class="p-8 relative z-10">
                            <div class="flex items-center gap-3 mb-3">
                                <span class="text-xs font-medium text-orange-300/80">2023</span>
                                <span class="w-1 h-1 bg-orange-300/50 rounded-full"></span>
                                <span class="text-xs font-medium text-orange-300/80">GAME</span>
                            </div>
                            <h3 class="font-bold text-2xl mb-3 text-white/90 group-hover:text-white transition-colors duration-300">Mini JavaScript Game</h3>
                            <p class="mb-6 text-white/70 group-hover:text-white/90 text-sm leading-relaxed">A collection of browser-based mini games built with vanilla JavaScript for learning and fun. Features interactive gameplay and responsive design.</p>

                            <div class="flex flex-wrap gap-2 mb-6">
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">JavaScript</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">HTML5 Canvas</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">CSS3</span>
                                <span class="px-3 py-1 text-xs font-medium text-white/60 border border-white/10 rounded-full">Game Logic</span>
                            </div>

                            <div class="flex items-center gap-3">
                                <a href="#" class="group/btn flex-1 relative px-6 py-2 text-sm font-medium text-white transition-all duration-300 ease-in-out hover:scale-105">
                                    <span class="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-full blur group-hover/btn:bg-gradient-to-r group-hover/btn:from-orange-500/30 group-hover/btn:to-red-500/30"></span>
                                    <span class="relative flex items-center justify-center">
                                        <i class="fas fa-play mr-2 opacity-70 group-hover/btn:opacity-100"></i>
                                        Play Game
                                    </span>
                                </a>
                                <a href="#" target="_blank" class="relative p-2 text-white/70 hover:text-white transition-colors duration-300">
                                    <i class="fab fa-github fa-lg"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
            </div>
        </section>
    </main>
    <!-- Unified Premium Footer Start -->
    <footer class="footer-premium mt-16 relative">
        <div class="footer-glow"></div>
        <div class="footer-content container mx-auto px-5 py-10 grid grid-cols-1 md:grid-cols-4 gap-10">
            <div>
                <a href="https://linktr.ee/abdulbasitmemon" target="_blank" class="flex flex-col items-center mb-4">
                    <img src="../images/logo-removebg-preview.png" class="h-24 transition-transform transform hover:scale-110" alt="ABM Logo" />
                    <span class="mt-2 text-white font-semibold text-lg dark:text-white">Abdul Basit Memon</span>
                </a>
            </div>
            <div>
                <h2 class="mb-4 text-lg font-bold text-secondary uppercase tracking-widest">Visit Here</h2>
                <ul class="space-y-2">
                    <li><a href="../index.html#services" class="footer-link">Services</a></li>
                    <li><a href="./projects.html" class="footer-link">Projects</a></li>
                    <li><a href="https://abdulbasitmemon.notion.site/d9cdb927bf1f48b6ad6abc54cf5b86a1?pvs=74" target="_blank" class="footer-link">My Notion</a></li>
                </ul>
            </div>
            <div>
                <h2 class="mb-4 text-lg font-bold text-secondary uppercase tracking-widest">Prologware Solutions</h2>
                <ul class="space-y-2">
                    <li><a href="https://resources-heaven.super.site/" target="_blank" class="footer-link">Resources Heaven</a></li>
                    <li><a href="https://chat.whatsapp.com/H4mF4unGvDI3vgCFMDl3Vs" target="_blank" class="footer-link">Internships & Jobs</a></li>
                    <li><a href="https://xtiles.app/65a2a3cc83f7541b54f335f8" target="_blank" class="footer-link">Community</a></li>
                </ul>
            </div>
            <div>
                <h2 class="mb-4 text-lg font-bold text-secondary uppercase tracking-widest">Additional Info</h2>
                <ul class="space-y-2">
                    <li><a href="../community-guidelines.html" class="footer-link">Community Guideline</a></li>
                    <li><a href="https://docs.google.com/forms/d/e/1FAIpQLSdKa4MB9goqYJYTbNhQCNE1GCjIBV43OazjQ2KU2CTXUHrNWQ/viewform" target="_blank" class="footer-link">Suggest &amp; Request</a></li>
                    <li><a href="#" class="footer-link">Events</a></li>
                </ul>
            </div>
        </div>
        <hr class="footer-divider my-6 mx-auto w-11/12" />
        <div class="footer-content container mx-auto px-5 py-4 flex flex-col md:flex-row justify-between items-center">
            <span class="footer-copyright">© 2024 <a href="https://linktr.ee/abdulbasitmemon" target="_blank" class="footer-link">Abdul Basit MEMON™</a>. All Rights Reserved.</span>
            <div class="flex flex-row gap-4 mt-4 md:mt-0 items-center justify-center">
                <a href="https://www.linkedin.com/in/abdul-basit-memon-614961166/" target="_blank" class="footer-social flex items-center justify-center rounded-full w-10 h-10 bg-white/10 hover:bg-secondary transition" aria-label="LinkedIn"><i class="fa-brands fa-linkedin-in fa-lg"></i></a>
                <a href="https://x.com/AbdAbdulbasit1" target="_blank" class="footer-social flex items-center justify-center rounded-full w-10 h-10 bg-white/10 hover:bg-secondary transition" aria-label="Twitter"><i class="fab fa-twitter fa-lg"></i></a>
                <a href="https://github.com/abm1119" class="footer-social flex items-center justify-center rounded-full w-10 h-10 bg-white/10 hover:bg-secondary transition" aria-label="GitHub"><i class="fa-brands fa-github fa-lg"></i></a>
                <a href="https://nas.io/prologware-solutions-3" class="footer-social flex items-center justify-center rounded-full w-10 h-10 bg-white/10 hover:bg-secondary transition" aria-label="WhatsApp"><i class="fa-brands fa-whatsapp fa-lg"></i></a>
                <a href="https://www.instagram.com/abmemon.memon.9/" class="footer-social flex items-center justify-center rounded-full w-10 h-10 bg-white/10 hover:bg-secondary transition" aria-label="Instagram"><i class="fa-brands fa-instagram fa-lg"></i></a>
            </div>
        </div>
    </footer>

    <style>
        /* Category Filter Styles - Following index.html theme */
        .category-filter {
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            background: rgba(51, 65, 85, 0.6);
            border: 1px solid rgba(99, 102, 241, 0.2);
            border-radius: 0.75rem;
            color: #e2e8f0;
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        .category-filter:hover {
            transform: translateY(-2px);
            border-color: rgba(99, 102, 241, 0.4);
            background: rgba(99, 102, 241, 0.2);
            color: white;
        }

        .category-filter.active {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.8) 0%, rgba(139, 92, 246, 0.6) 100%);
            border-color: rgba(99, 102, 241, 0.6);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
        }

        .category-filter i {
            color: #818cf8;
        }

        .category-filter.active i {
            color: white;
        }

        /* Project Item Animation */
        .project-item {
            opacity: 1;
            transform: translateY(0);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .project-item.hidden {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
            pointer-events: none;
        }

        /* Responsive Design for Category Filters */
        @media (max-width: 768px) {
            .category-filter {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
            }
        }
    </style>

    <script>
        // Enhanced JavaScript for Projects Page - Following index.html theme
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        const navbarHeight = document.querySelector('.navbar').offsetHeight;
                        const targetPosition = target.offsetTop - navbarHeight - 20;

                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Category Filter Functionality
            const filterButtons = document.querySelectorAll('.category-filter');
            const projectCards = document.querySelectorAll('.project-item');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');

                    // Update active button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Filter projects with smooth animation
                    projectCards.forEach((card, index) => {
                        const cardCategory = card.getAttribute('data-category');

                        if (category === 'all' || cardCategory === category) {
                            // Show card
                            card.classList.remove('hidden');
                            setTimeout(() => {
                                card.style.opacity = '1';
                                card.style.transform = 'translateY(0) scale(1)';
                            }, index * 50);
                        } else {
                            // Hide card
                            card.style.opacity = '0';
                            card.style.transform = 'translateY(20px) scale(0.95)';
                            setTimeout(() => {
                                card.classList.add('hidden');
                            }, 300);
                        }
                    });
                });
            });

            // Navbar scroll effect
            const navbar = document.querySelector('.navbar');
            window.addEventListener('scroll', () => {
                const currentScrollY = window.scrollY;
                if (currentScrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // Initialize AOS-like animations for project cards
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all project cards
            projectCards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                observer.observe(card);
            });

            // Add focus management for accessibility
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    // Close mobile menu on escape
                    const mobileMenu = document.querySelector('[x-data]');
                    if (mobileMenu && mobileMenu.__x) {
                        mobileMenu.__x.$data.isOpen = false;
                    }
                }
            });

            // Add loading states for better UX
            window.addEventListener('load', () => {
                document.body.classList.add('loaded');
            });
        });
    </script>
</body>
</html>
