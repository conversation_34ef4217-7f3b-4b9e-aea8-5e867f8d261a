import React from 'react';

const Prologware = () => {
  return (
    <section id="prologware" className="py-24">
      <div className="container mx-auto px-6">
        <h2 className="section-heading text-4xl text-center mb-16">Prologware Solutions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-16 items-center">
          <div className="relative rounded-2xl overflow-hidden shadow-lg">
            <iframe 
              className="w-full h-96" 
              src="https://www.youtube.com/embed/videoID" 
              frameBorder="0" 
              allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" 
              allowFullScreen
              title="Prologware Solutions Video"
            ></iframe>
          </div>
          <div className="card p-10">
            <p className="text-2xl text-secondary-color mb-8 leading-relaxed">
              Prologware Solutions focuses on community building with the motto of <span className="font-bold text-primary-color">To Learn, Grow, <PERSON><PERSON><PERSON> & Collab.</span>
            </p>
            <ul className="space-y-6">
              <li className="flex items-center space-x-4">
                <i className="fas fa-star text-2xl text-primary-color"></i>
                <span className="text-lg">Premium Courses</span>
              </li>
              <li className="flex items-center space-x-4">
                <i className="fas fa-tools text-2xl text-primary-color"></i>
                <span className="text-lg">Tools and Resources</span>
              </li>
              <li className="flex items-center space-x-4">
                <i className="fas fa-graduation-cap text-2xl text-primary-color"></i>
                <span className="text-lg">Scholarship Updates</span>
              </li>
              <li className="flex items-center space-x-4">
                <i className="fas fa-briefcase text-2xl text-primary-color"></i>
                <span className="text-lg">Internships and Jobs</span>
              </li>
              <li className="flex items-center space-x-4">
                <i className="fas fa-users text-2xl text-primary-color"></i>
                <span className="text-lg">Community Collaboration</span>
              </li>
            </ul>
            <a 
              href="https://nas.io/prologware-solutions-3" 
              target="_blank" 
              rel="noopener noreferrer"
              className="inline-block bg-primary-color text-white py-4 px-10 rounded-full text-lg font-semibold hover:bg-secondary-color transition duration-300 transform hover:scale-105 shadow-lg mt-10"
            >
              Join Our Community
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Prologware;
