import React, { useState } from 'react';

const Prologware = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const features = [
    {
      icon: "fas fa-star",
      title: "Premium Courses",
      description: "Curated learning paths designed by industry experts",
      color: "text-yellow-400",
      bgColor: "bg-yellow-400/10"
    },
    {
      icon: "fas fa-tools",
      title: "Tools & Resources",
      description: "Essential development tools and resource libraries",
      color: "text-blue-400",
      bgColor: "bg-blue-400/10"
    },
    {
      icon: "fas fa-graduation-cap",
      title: "Scholarship Updates",
      description: "Latest opportunities for educational funding",
      color: "text-green-400",
      bgColor: "bg-green-400/10"
    },
    {
      icon: "fas fa-briefcase",
      title: "Internships & Jobs",
      description: "Career opportunities and professional networking",
      color: "text-purple-400",
      bgColor: "bg-purple-400/10"
    },
    {
      icon: "fas fa-users",
      title: "Community Collaboration",
      description: "Connect with like-minded developers and creators",
      color: "text-indigo-400",
      bgColor: "bg-indigo-400/10"
    },
    {
      icon: "fas fa-rocket",
      title: "Startup Support",
      description: "Guidance and resources for launching your ideas",
      color: "text-pink-400",
      bgColor: "bg-pink-400/10"
    }
  ];

  const stats = [
    { number: "5000+", label: "Community Members", icon: "fas fa-users" },
    { number: "200+", label: "Premium Courses", icon: "fas fa-graduation-cap" },
    { number: "1000+", label: "Job Placements", icon: "fas fa-briefcase" },
    { number: "50+", label: "Industry Partners", icon: "fas fa-handshake" }
  ];

  const tabs = [
    { id: 'overview', label: 'Overview', icon: 'fas fa-eye' },
    { id: 'features', label: 'Features', icon: 'fas fa-star' },
    { id: 'community', label: 'Community', icon: 'fas fa-users' },
    { id: 'resources', label: 'Resources', icon: 'fas fa-book' }
  ];

  return (
    <section id="prologware" className="py-24 bg-gradient-to-b from-bg-secondary/60 via-bg-primary to-bg-secondary/60 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/3 left-1/5 w-96 h-96 bg-gradient-to-br from-indigo-500/10 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/3 right-1/5 w-80 h-80 bg-gradient-to-tr from-purple-500/8 to-transparent rounded-full blur-3xl animate-pulse-slow2"></div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        {/* Header Section */}
        <div className="text-center mb-20" data-animate>
          <div className="inline-flex items-center gap-3 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 rounded-full px-6 py-3 mb-6 border border-indigo-500/30">
            <i className="fas fa-building text-indigo-400"></i>
            <span className="text-indigo-300 font-medium">Prologware Solutions</span>
          </div>
          <h2 className="section-heading mb-6">Building Tomorrow's Tech Community</h2>
          <div className="w-32 h-1 bg-gradient-to-r from-indigo-500 via-blue-500 to-purple-500 mx-auto rounded-full mb-8"></div>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed font-light">
            Empowering developers, creators, and innovators through collaborative learning and growth opportunities.
            <span className="block mt-3 text-2xl font-bold bg-gradient-to-r from-indigo-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
              "To Learn, Grow, Earn & Collab"
            </span>
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-20" data-animate>
          {stats.map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="bg-gradient-to-br from-slate-800/60 to-slate-900/60 rounded-2xl p-6 border border-indigo-500/20 backdrop-blur-sm hover:border-indigo-400/40 transition-all duration-300 hover:-translate-y-1">
                <i className={`${stat.icon} text-3xl text-indigo-400 mb-4 group-hover:text-indigo-300 transition-colors`}></i>
                <div className="text-3xl font-bold text-white mb-2">{stat.number}</div>
                <div className="text-slate-400 text-sm font-medium">{stat.label}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start mb-20">
          {/* Video Section */}
          <div className="relative" data-animate>
            <div className="relative rounded-3xl overflow-hidden shadow-2xl border border-indigo-500/20 backdrop-blur-sm">
              <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 via-blue-500/5 to-purple-500/10"></div>
              <div className="relative aspect-video bg-slate-900/50 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-full flex items-center justify-center mb-4 mx-auto shadow-lg hover:scale-110 transition-transform cursor-pointer">
                    <i className="fas fa-play text-white text-2xl ml-1"></i>
                  </div>
                  <p className="text-slate-300 font-medium">Watch Our Community Story</p>
                  <p className="text-slate-500 text-sm mt-1">Coming Soon</p>
                </div>
              </div>
            </div>

            {/* Floating Elements */}
            <div className="absolute -top-6 -right-6 w-24 h-24 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse-slow"></div>
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-gradient-to-br from-blue-400/15 to-indigo-400/15 rounded-full blur-xl animate-pulse-slow2"></div>
          </div>

          {/* Content Tabs */}
          <div className="space-y-8" data-animate>
            {/* Tab Navigation */}
            <div className="flex flex-wrap gap-2 p-2 bg-slate-800/50 rounded-2xl border border-slate-700/50">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-4 py-3 rounded-xl font-medium transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-indigo-500 to-blue-500 text-white shadow-lg'
                      : 'text-slate-400 hover:text-white hover:bg-slate-700/50'
                  }`}
                >
                  <i className={tab.icon}></i>
                  <span className="hidden sm:inline">{tab.label}</span>
                </button>
              ))}
            </div>

            {/* Tab Content */}
            <div className="bg-gradient-to-br from-slate-800/60 to-slate-900/60 rounded-3xl p-8 border border-indigo-500/20 backdrop-blur-sm min-h-[400px]">
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-white mb-4">Community Overview</h3>
                  <p className="text-slate-300 leading-relaxed text-lg">
                    Prologware Solutions is more than just a community—it's a thriving ecosystem where technology enthusiasts,
                    students, and professionals come together to learn, share knowledge, and build the future of tech.
                  </p>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-8">
                    <div className="flex items-center gap-3 p-4 bg-indigo-500/10 rounded-xl border border-indigo-500/20">
                      <i className="fas fa-globe text-indigo-400 text-xl"></i>
                      <div>
                        <div className="font-semibold text-white">Global Reach</div>
                        <div className="text-slate-400 text-sm">50+ Countries</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-4 bg-green-500/10 rounded-xl border border-green-500/20">
                      <i className="fas fa-clock text-green-400 text-xl"></i>
                      <div>
                        <div className="font-semibold text-white">24/7 Support</div>
                        <div className="text-slate-400 text-sm">Always Available</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'features' && (
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-white mb-6">Platform Features</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {features.map((feature, index) => (
                      <div key={index} className={`p-4 ${feature.bgColor} rounded-xl border border-slate-600/30 hover:border-slate-500/50 transition-all duration-300 hover:-translate-y-1`}>
                        <div className="flex items-start gap-3">
                          <i className={`${feature.icon} ${feature.color} text-xl mt-1`}></i>
                          <div>
                            <h4 className="font-semibold text-white mb-1">{feature.title}</h4>
                            <p className="text-slate-400 text-sm">{feature.description}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'community' && (
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-white mb-4">Join Our Community</h3>
                  <p className="text-slate-300 leading-relaxed">
                    Connect with thousands of developers, participate in hackathons, attend virtual meetups,
                    and collaborate on exciting projects that make a real impact.
                  </p>
                  <div className="space-y-4 mt-6">
                    <div className="flex items-center gap-4 p-4 bg-slate-700/30 rounded-xl">
                      <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-full flex items-center justify-center">
                        <i className="fas fa-comments text-white"></i>
                      </div>
                      <div>
                        <div className="font-semibold text-white">Active Discussions</div>
                        <div className="text-slate-400 text-sm">Join daily tech conversations</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4 p-4 bg-slate-700/30 rounded-xl">
                      <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                        <i className="fas fa-code text-white"></i>
                      </div>
                      <div>
                        <div className="font-semibold text-white">Code Reviews</div>
                        <div className="text-slate-400 text-sm">Get feedback from experts</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'resources' && (
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-white mb-4">Learning Resources</h3>
                  <p className="text-slate-300 leading-relaxed">
                    Access our comprehensive library of tutorials, documentation, templates, and tools
                    designed to accelerate your learning journey.
                  </p>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-6">
                    <div className="p-4 bg-blue-500/10 rounded-xl border border-blue-500/20">
                      <i className="fas fa-book text-blue-400 text-2xl mb-3"></i>
                      <h4 className="font-semibold text-white mb-2">Documentation</h4>
                      <p className="text-slate-400 text-sm">Comprehensive guides and tutorials</p>
                    </div>
                    <div className="p-4 bg-purple-500/10 rounded-xl border border-purple-500/20">
                      <i className="fas fa-code text-purple-400 text-2xl mb-3"></i>
                      <h4 className="font-semibold text-white mb-2">Code Templates</h4>
                      <p className="text-slate-400 text-sm">Ready-to-use project starters</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center" data-animate>
          <div className="bg-gradient-to-r from-slate-800/60 via-slate-800/40 to-slate-800/60 rounded-3xl p-12 border border-indigo-500/20 backdrop-blur-lg">
            <h3 className="text-3xl font-bold text-white mb-4">Ready to Join the Community?</h3>
            <p className="text-slate-300 text-lg mb-8 max-w-2xl mx-auto">
              Become part of a thriving community that's shaping the future of technology through collaboration and innovation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://nas.io/prologware-solutions-3"
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-primary btn-lg group"
              >
                <i className="fas fa-users mr-2"></i>
                Join Community
                <i className="fas fa-arrow-right group-hover:translate-x-1 transition-transform ml-2"></i>
              </a>
              <a
                href="https://resources-heaven.super.site/"
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-secondary btn-lg group"
              >
                <i className="fas fa-book mr-2"></i>
                Explore Resources
                <i className="fas fa-external-link-alt group-hover:scale-110 transition-transform ml-2"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Prologware;
